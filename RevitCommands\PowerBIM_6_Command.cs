﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.ApplicationServices;
using Common.UI.Forms;
using BecaTransactionsNamesManager;
using System.Diagnostics;
using System.Windows.Forms;
using BecaRevitUtilities.ElementUtilities;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using Autodesk.Revit.UI.Selection;
using System.Net;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_5.UI.Forms;
using MEP.PowerBIM_6.Services;

namespace MEP.PowerBIM_6.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    public class PowerBIM_6_Command : BecaBaseCommand
    {
        public PowerBIM_ProjectInfo projInfo;
        public List<PowerBIM_DBData> DBs;

        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get Revit application and document
                UIApplication uiapp = commandData.Application;
                UIDocument uidoc = uiapp.ActiveUIDocument;
                Document doc = uidoc.Document;

                // Start logging
                _taskLogger.PreTaskStart();

                // Check if PowerBIM is already open
                if (WindowService.IsMainWindowOpen)
                {
                    WindowService.ShowMessage("PowerBIM", "PowerBIM is already open.");
                    _taskLogger.PostTaskEnd("PowerBIM already opened");
                    return Result.Succeeded;
                }

                // Validate project has required elements
                if (!HasRequiredElements(doc))
                {
                    WindowService.ShowMessage("PowerBIM Error",
                        "This project doesn't contain the required electrical elements.\n\n" +
                        "Please ensure your project has:\n" +
                        "- Electrical Equipment (Distribution Boards)\n" +
                        "- Electrical Circuits\n" +
                        "- Panel Schedule Views");
                    _taskLogger.PostTaskEnd("Required elements missing");
                    return Result.Cancelled;
                }

                // Show the main PowerBIM window
                WindowService.ShowMainWindow(uiapp, _taskLogger);

                _taskLogger.PostTaskEnd("PowerBIM WPF opened successfully");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                _taskLogger.PostTaskEnd($"PowerBIM failed: {ex.Message}");
                WindowService.ShowMessage("PowerBIM Error", $"An error occurred: {ex.Message}");
                return Result.Failed;
            }
        }

        /// <summary>
        /// Checks if the document has the required elements for PowerBIM
        /// </summary>
        private bool HasRequiredElements(Document doc)
        {
            try
            {
                // Check for electrical equipment
                var electricalEquipmentCollector = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                    .WhereElementIsNotElementType();

                if (!electricalEquipmentCollector.Any())
                    return false;

                // Check for electrical circuits
                var electricalCircuitCollector = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_ElectricalCircuit)
                    .WhereElementIsNotElementType();

                if (!electricalCircuitCollector.Any())
                    return false;

                // Check for panel schedule views
                var panelScheduleCollector = new FilteredElementCollector(doc)
                    .OfClass(typeof(PanelScheduleView));

                if (!panelScheduleCollector.Any())
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public override string GetAddinAuthor()
        {
            return "Tristan Balme, Harry Billinge, Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.PowerBIM6.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
