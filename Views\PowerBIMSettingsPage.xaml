﻿<Page x:Class="MEP.PowerBIM_6.Views.PowerBIMSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      Background="White"
      Title="Settings">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!-- Header -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" 
                                         VerticalAlignment="Center" Margin="0,0,8,0" />
                <TextBlock Text="PowerBIM Settings" 
                           Style="{StaticResource MaterialDesignHeadline5TextBlock}" 
                           VerticalAlignment="Center" />
            </StackPanel>

            <!-- Calculation Settings -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock Text="Calculation Settings" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <!-- Voltage Drop Settings -->
                    <GroupBox Header="Voltage Drop Settings" Margin="0,0,0,16">
                        <StackPanel Margin="8">
                            <TextBlock Text="System Maximum Voltage Drop:" Margin="0,0,0,8" />
                            <StackPanel Orientation="Horizontal">
                                <RadioButton Content="5%" 
                                             IsChecked="{Binding SystemVD5Percent}" 
                                             Margin="0,0,16,0" />
                                <RadioButton Content="7%" 
                                             IsChecked="{Binding SystemVD7Percent}" />
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!-- Cable Selection -->
                    <GroupBox Header="Cable Selection Standard" Margin="0,0,0,16">
                        <StackPanel Margin="8">
                            <TextBlock Text="Ambient Temperature Standard:" Margin="0,0,0,8" />
                            <StackPanel Orientation="Horizontal">
                                <RadioButton Content="New Zealand (30°C)" 
                                             IsChecked="{Binding NZCableSelection}" 
                                             Margin="0,0,16,0" />
                                <RadioButton Content="Australia (40°C)" 
                                             IsChecked="{Binding AUSCableSelection}" />
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!-- Advanced Settings -->
                    <GroupBox Header="Advanced Calculation Settings">
                        <Grid Margin="8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Discrimination Test Multiplier:" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                     Text="{Binding ProjectInfo.DiscriminationTestMultiplier}" 
                                     Margin="0,0,0,8" />

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Ambient Temperature (°C):" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="1" Grid.Column="1" 
                                     Text="{Binding ProjectInfo.AmbientTemp}" 
                                     Margin="0,0,0,8" />

                            <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" 
                                      Content="GPO Calculation at 80%" 
                                      IsChecked="{Binding ProjectInfo.GPOCalc80Perc}" />
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </materialDesign:Card>

            <!-- Project Information -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock Text="Project Information" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Job Name:" 
                                   VerticalAlignment="Center" Margin="0,0,8,8" />
                        <TextBox Grid.Row="0" Grid.Column="1" 
                                 Text="{Binding ProjectInfo.JobName}" 
                                 Margin="0,0,0,8" />

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Job Number:" 
                                   VerticalAlignment="Center" Margin="0,0,8,8" />
                        <TextBox Grid.Row="1" Grid.Column="1" 
                                 Text="{Binding ProjectInfo.JobNumber}" 
                                 Margin="0,0,0,8" />

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Engineer:" 
                                   VerticalAlignment="Center" Margin="0,0,8,8" />
                        <TextBox Grid.Row="2" Grid.Column="1" 
                                 Text="{Binding ProjectInfo.Engineer}" 
                                 Margin="0,0,0,8" />

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Verifier:" 
                                   VerticalAlignment="Center" Margin="0,0,8,8" />
                        <TextBox Grid.Row="3" Grid.Column="1" 
                                 Text="{Binding ProjectInfo.Verifier}" 
                                 Margin="0,0,0,8" />

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Revision:" 
                                   VerticalAlignment="Center" Margin="0,0,8,0" />
                        <TextBox Grid.Row="4" Grid.Column="1" 
                                 Text="{Binding ProjectInfo.Revision}" />
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Database Settings -->
            <materialDesign:Card Padding="16">
                <StackPanel>
                    <TextBlock Text="Database Settings" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Database Path:" 
                                   VerticalAlignment="Center" Margin="0,0,8,0" />
                        <TextBox Grid.Column="1" 
                                 Text="{Binding ProjectInfo.DatabasePath}" 
                                 IsReadOnly="True" 
                                 Margin="0,0,8,0" />
                        <Button Grid.Column="2" Content="Browse..." 
                                />
                    </Grid>

                    <TextBlock Text="Database status and information will be displayed here." 
                               Margin="0,16,0,0" 
                               Foreground="{DynamicResource MaterialDesignBodyLight}" 
                               FontStyle="Italic" />
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</Page>
