﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.ViewModels;
using MEP.PowerBIM_6.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Application = System.Windows.Application;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Service for managing WPF windows in a modeless manner
    /// </summary>
    internal class WindowService
    {
        private static MainWindowEnhanced _mainWindow;
        private static PowerBIMMainViewModelEnhanced _mainViewModel;

        /// <summary>
        /// Shows the main PowerBIM window
        /// </summary>
        public static void ShowMainWindow(UIApplication uiApplication, BecaActivityLoggerData logger)
        {
            try
            {
                // If we do not have a window yet, create and show it
                if (_mainWindow == null || !_mainWindow.IsLoaded)
                {
                    // Create external event handler
                    var requestHandler = new RequestHandler(uiApplication, logger);
                    var externalEvent = ExternalEvent.Create(requestHandler);

                    // Create ViewModel
                    _mainViewModel = new PowerBIMMainViewModelEnhanced(externalEvent, requestHandler, logger, uiApplication);

                    // Create and show window
                    _mainWindow = new MainWindowEnhanced(_mainViewModel);
                    _mainWindow.Show();

                    // Initialize the ViewModel asynchronously
                    _ = Task.Run(async () => await _mainViewModel.InitializeAsync());
                }
                else
                {
                    // Bring existing window to front
                    _mainWindow.Activate();
                    _mainWindow.WindowState = WindowState.Normal;
                }
            }
            catch (Exception ex)
            {
                logger?.PostTaskEnd($"Error showing main window: {ex.Message}");
                System.Windows.MessageBox.Show($"Error opening PowerBIM: {ex.Message}", "PowerBIM Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Closes the main window
        /// </summary>
        public static void CloseMainWindow()
        {
            try
            {
                if (_mainWindow != null)
                {
                    _mainWindow.Close();
                    _mainWindow = null;
                    _mainViewModel = null;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw during shutdown
                System.Diagnostics.Debug.WriteLine($"Error closing main window: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows a message to the user
        /// </summary>
        public static void ShowMessage(string title, string message, MessageBoxImage icon = MessageBoxImage.Information)
        {
            Application.Current?.Dispatcher.Invoke(() =>
            {
                System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, icon);
            });
        }

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        public static bool ShowConfirmation(string title, string message)
        {
            bool result = false;
            Application.Current?.Dispatcher.Invoke(() =>
            {
                result = System.Windows.MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
            });
            return result;
        }

        /// <summary>
        /// Gets whether the main window is currently open
        /// </summary>
        public static bool IsMainWindowOpen => _mainWindow != null && _mainWindow.IsLoaded;

        /// <summary>
        /// Called when Revit is shutting down
        /// </summary>
        public static void OnRevitShutdown()
        {
            CloseMainWindow();
        }
    }
}
