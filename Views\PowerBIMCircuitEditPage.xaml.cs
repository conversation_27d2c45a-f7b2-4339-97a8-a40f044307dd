﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMCircuitEditPage.xaml
    /// </summary>
    public partial class PowerBIMCircuitEditPage : Page
    {
        public PowerBIMCircuitEditPage()
        {
            InitializeComponent();

            // Inherit DataContext from parent window
            this.Loaded += (s, e) =>
            {
                if (DataContext == null)
                {
                    var window = Window.GetWindow(this);
                    if (window != null)
                        DataContext = window.DataContext;
                }
            };
        }
    }
}
