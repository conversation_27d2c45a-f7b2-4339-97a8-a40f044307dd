﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PowerBIMHelpDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PowerBIM Help"
    Width="900"
    Height="700"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Border
            Grid.Row="0"
            Padding="16,12"
            Background="{DynamicResource PrimaryHueMidBrush}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon
                    Width="24"
                    Height="24"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center"
                    Foreground="White"
                    Kind="HelpCircle" />
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="White"
                    Text="PowerBIM 1.5 WPF - Help and Documentation" />
            </StackPanel>
        </Border>

        <!--  Content  -->
        <TabControl
            Grid.Row="1"
            Margin="16"
            Style="{StaticResource MaterialDesignTabControl}">
            <!--  Getting Started Tab  -->
            <TabItem Header="Getting Started">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                            Text="Getting Started with PowerBIM 1.5 WPF" />

                        <TextBlock Margin="0,0,0,16" TextWrapping="Wrap">
                            PowerBIM 1.5 WPF is a comprehensive electrical circuit analysis tool for Autodesk Revit.
                            This modern WPF version provides enhanced user experience and improved performance.
                        </TextBlock>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Quick Start Steps:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="1. Load Project Data - Click 'Load Project Data' to import distribution boards from your Revit model" />
                            <TextBlock Margin="0,0,0,4" Text="2. Select Distribution Boards - Choose which DBs you want to analyze" />
                            <TextBlock Margin="0,0,0,4" Text="3. Run Auto Sizer - Click 'Run Auto Sizer' to perform calculations" />
                            <TextBlock Margin="0,0,0,4" Text="4. Review Results - Check the Pass/Warning/Fail counts for each DB" />
                            <TextBlock Margin="0,0,0,4" Text="5. Save to Revit - Click 'Save to Revit' to commit your results" />
                        </StackPanel>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Navigation:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Home - Overview and quick actions" />
                            <TextBlock Margin="0,0,0,4" Text="• Distribution Boards - Main analysis interface" />
                            <TextBlock Margin="0,0,0,4" Text="• Circuits - Detailed circuit editing" />
                            <TextBlock Margin="0,0,0,4" Text="• Results - Analysis results and reports" />
                            <TextBlock Margin="0,0,0,4" Text="• Settings - Project and calculation settings" />
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!--  Features Tab  -->
            <TabItem Header="Features">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                            Text="PowerBIM Features" />

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Electrical Analysis:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Automatic cable sizing based on AS/NZS 3008" />
                            <TextBlock Margin="0,0,0,4" Text="• Voltage drop calculations (5% or 7% system maximum)" />
                            <TextBlock Margin="0,0,0,4" Text="• Circuit breaker analysis and selection" />
                            <TextBlock Margin="0,0,0,4" Text="• Fault current calculations (PSCC)" />
                            <TextBlock Margin="0,0,0,4" Text="• Earth fault loop impedance (EFLI) calculations" />
                            <TextBlock Margin="0,0,0,4" Text="• Discrimination testing" />
                        </StackPanel>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Standards Compliance:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• AS/NZS 3008 - Cable Selection" />
                            <TextBlock Margin="0,0,0,4" Text="• AS/NZS 3000 - Wiring Rules" />
                            <TextBlock Margin="0,0,0,4" Text="• IEC 60364 - Electrical Installations" />
                            <TextBlock Margin="0,0,0,4" Text="• New Zealand (30°C) and Australian (40°C) standards" />
                        </StackPanel>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Data Management:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Export results to Excel/CSV" />
                            <TextBlock Margin="0,0,0,4" Text="• Import circuit data from Excel" />
                            <TextBlock Margin="0,0,0,4" Text="• User notes and system notes" />
                            <TextBlock Margin="0,0,0,4" Text="• Project information management" />
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!--  Troubleshooting Tab  -->
            <TabItem Header="Troubleshooting">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                            Text="Common Issues and Solutions" />

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="No Distribution Boards Found:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Ensure your Revit model contains electrical panels" />
                            <TextBlock Margin="0,0,0,4" Text="• Check that panels have the required PowerBIM parameters" />
                            <TextBlock Margin="0,0,0,4" Text="• Verify the model is properly set up for electrical analysis" />
                        </StackPanel>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Calculation Errors:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Check that all required electrical parameters are filled" />
                            <TextBlock Margin="0,0,0,4" Text="• Verify EFLI values are realistic (typically 0.1-2.0 Ω)" />
                            <TextBlock Margin="0,0,0,4" Text="• Ensure PSCC values are appropriate for your installation" />
                            <TextBlock Margin="0,0,0,4" Text="• Check circuit paths are properly defined in Revit" />
                        </StackPanel>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Performance Issues:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Close other applications to free up memory" />
                            <TextBlock Margin="0,0,0,4" Text="• Work with smaller sections of your model" />
                            <TextBlock Margin="0,0,0,4" Text="• Ensure your Revit model is optimized" />
                        </StackPanel>

                        <TextBlock
                            Margin="0,0,0,8"
                            FontWeight="SemiBold"
                            Text="Database Issues:" />
                        <StackPanel Margin="16,0,0,16">
                            <TextBlock Margin="0,0,0,4" Text="• Verify the cable database path is correct" />
                            <TextBlock Margin="0,0,0,4" Text="• Check that the database file is not corrupted" />
                            <TextBlock Margin="0,0,0,4" Text="• Ensure you have read access to the database location" />
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!--  Support Tab  -->
            <TabItem Header="Support">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                            Text="Support and Contact Information" />

                        <materialDesign:Card Margin="0,0,0,16" Padding="16">
                            <StackPanel>
                                <TextBlock
                                    Margin="0,0,0,8"
                                    FontSize="16"
                                    FontWeight="SemiBold"
                                    Text="Beca Support" />
                                <TextBlock Margin="0,0,0,8" Text="For technical support and assistance:" />
                                <StackPanel Margin="16,0,0,16">
                                    <TextBlock Margin="0,0,0,4" Text="• Email: <EMAIL>" />
                                    <TextBlock Margin="0,0,0,4" Text="• Website: www.beca.com" />
                                    <TextBlock Margin="0,0,0,4" Text="• Phone: Contact your local Beca office" />
                                </StackPanel>

                                <Button
                                    HorizontalAlignment="Left"
                                    Click="VisitWebsite_Click"
                                    Content="Visit Beca Website" />
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Margin="0,0,0,16" Padding="16">
                            <StackPanel>
                                <TextBlock
                                    Margin="0,0,0,8"
                                    FontSize="16"
                                    FontWeight="SemiBold"
                                    Text="Documentation" />
                                <TextBlock Margin="0,0,0,8" Text="Additional resources and documentation:" />
                                <StackPanel Margin="16,0,0,16">
                                    <TextBlock Margin="0,0,0,4" Text="• User Manual: Available in the installation directory" />
                                    <TextBlock Margin="0,0,0,4" Text="• Video Tutorials: Available on the Beca SharePoint" />
                                    <TextBlock Margin="0,0,0,4" Text="• Best Practices Guide: Contact support for access" />
                                </StackPanel>

                                <Button
                                    HorizontalAlignment="Left"
                                    Click="OpenSharePoint_Click"
                                    Content="Open SharePoint" />
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock
                                    Margin="0,0,0,8"
                                    FontSize="16"
                                    FontWeight="SemiBold"
                                    Text="Version Information" />
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="0,0,16,4"
                                        FontWeight="SemiBold"
                                        Text="Version:" />
                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Margin="0,0,0,4"
                                        Text="1.5.0.0 WPF" />

                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Margin="0,0,16,4"
                                        FontWeight="SemiBold"
                                        Text="Build Date:" />
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        Margin="0,0,0,4"
                                        Text="2024-12-30" />

                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        Margin="0,0,16,0"
                                        FontWeight="SemiBold"
                                        Text="Framework:" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        Text="WPF with MVVM" />
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!--  Button Panel  -->
        <Border
            Grid.Row="2"
            Padding="16"
            Background="{DynamicResource MaterialDesignDivider}">
            <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                <Button
                    Click="Close_Click"
                    Content="Close"
                    IsDefault="True" />
            </StackPanel>
        </Border>
    </Grid>
</Window>
