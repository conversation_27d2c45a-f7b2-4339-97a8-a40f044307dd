﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Merge Material Design themes -->
    <ResourceDictionary.MergedDictionaries>
        <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Custom Styles -->

    <!-- Header Text Style -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}" />
        <Setter Property="Margin" Value="0,0,0,8" />
    </Style>

    <!-- Status Text Style -->
    <Style x:Key="StatusTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}" />
    </Style>

    <!-- Primary Button Style -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="MinWidth" Value="100" />
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="MinWidth" Value="100" />
    </Style>

    <!-- DataGrid Style -->
    <Style x:Key="PowerBIMDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False" />
        <Setter Property="CanUserAddRows" Value="False" />
        <Setter Property="CanUserDeleteRows" Value="False" />
        <Setter Property="SelectionMode" Value="Extended" />
        <Setter Property="GridLinesVisibility" Value="Horizontal" />
        <Setter Property="HeadersVisibility" Value="Column" />
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignDivider}" />
        <Setter Property="RowBackground" Value="Transparent" />
    </Style>

    <!-- GroupBox Style -->
    <Style x:Key="PowerBIMGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
        <Setter Property="Margin" Value="8" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryLight" />
    </Style>

    <!-- Status Colors -->
    <SolidColorBrush x:Key="PassBrush" Color="Green" />
    <SolidColorBrush x:Key="WarningBrush" Color="Orange" />
    <SolidColorBrush x:Key="FailBrush" Color="Red" />
    <SolidColorBrush x:Key="NeutralBrush" Color="Gray" />

    <!-- Data Templates -->

    <!-- Status Cell Template -->
    <DataTemplate x:Key="StatusCellTemplate">
        <Border CornerRadius="3" Padding="4,2">
            <Border.Style>
                <Style TargetType="Border">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding StatusColor}" Value="Green">
                            <Setter Property="Background" Value="{StaticResource PassBrush}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding StatusColor}" Value="Orange">
                            <Setter Property="Background" Value="{StaticResource WarningBrush}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding StatusColor}" Value="Red">
                            <Setter Property="Background" Value="{StaticResource FailBrush}" />
                        </DataTrigger>
                        <DataTrigger Binding="{Binding StatusColor}" Value="Gray">
                            <Setter Property="Background" Value="{StaticResource NeutralBrush}" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <TextBlock Text="{Binding Schedule_DB_Name}" Foreground="White" FontWeight="SemiBold" />
        </Border>
    </DataTemplate>

</ResourceDictionary>