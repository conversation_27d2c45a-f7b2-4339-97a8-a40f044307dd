﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PowerBIMMessageDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="{Binding Title}"
    Width="500"
    Height="300"
    MinWidth="400"
    MinHeight="200"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>

    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="16,12">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon x:Name="HeaderIcon" 
                                         Width="24" Height="24" 
                                         Foreground="White" 
                                         VerticalAlignment="Center" 
                                         Margin="0,0,8,0" />
                <TextBlock Text="{Binding Title}" 
                           Foreground="White" 
                           FontSize="16" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center" />
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
            <TextBlock x:Name="MessageText"
                       Text="{Binding Message}"
                       TextWrapping="Wrap"
                       FontSize="12"
                       LineHeight="18" />
        </ScrollViewer>

        <!-- Button Panel -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}" Padding="16">
            <StackPanel x:Name="ButtonPanel" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                <!-- Buttons will be added dynamically based on MessageBoxButton -->
            </StackPanel>
        </Border>
    </Grid>
</Window>
