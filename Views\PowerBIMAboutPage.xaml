﻿<Page x:Class="MEP.PowerBIM_6.Views.PowerBIMAboutPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      Background="White"
      Title="About">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="24" HorizontalAlignment="Center" MaxWidth="600">
            <!-- Logo and Title -->
            <StackPanel HorizontalAlignment="Center" Margin="0,0,0,32">
                <materialDesign:PackIcon Kind="Flash" Width="64" Height="64" 
                                         HorizontalAlignment="Center" 
                                         Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                         Margin="0,0,0,16" />
                <TextBlock Text="PowerBIM 6 WPF" 
                           Style="{StaticResource MaterialDesignHeadline3TextBlock}" 
                           HorizontalAlignment="Center" 
                           Margin="0,0,0,8" />
                <TextBlock Text="Electrical Circuit Analysis Tool" 
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}" 
                           HorizontalAlignment="Center" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}" />
            </StackPanel>

            <!-- Version Information -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock Text="Version Information" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Version:" FontWeight="SemiBold" Margin="0,0,16,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="6.0.0.0 WPF" Margin="0,0,0,8" />

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Build Date:" FontWeight="SemiBold" Margin="0,0,16,8" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="2025-06-30" Margin="0,0,0,8" />

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Framework:" FontWeight="SemiBold" Margin="0,0,16,8" />
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="WPF with MVVM" Margin="0,0,0,8" />

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Author:" FontWeight="SemiBold" Margin="0,0,16,0" />
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="Tristan Balme, Harry Billinge, Firza Utama" />
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Description -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock Text="About PowerBIM" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,16">
                        PowerBIM 1.5 WPF is a comprehensive electrical circuit analysis tool designed for Autodesk Revit. 
                        This WPF version represents a complete modernization of the original WinForms application, 
                        featuring improved user interface, better performance, and enhanced maintainability.
                    </TextBlock>

                    <TextBlock Text="Key Features:" FontWeight="SemiBold" Margin="0,0,0,8" />
                    <StackPanel Margin="16,0,0,16">
                        <TextBlock Text="• Automatic cable sizing and selection" Margin="0,0,0,4" />
                        <TextBlock Text="• Voltage drop calculations" Margin="0,0,0,4" />
                        <TextBlock Text="• Circuit breaker analysis" Margin="0,0,0,4" />
                        <TextBlock Text="• Fault current calculations" Margin="0,0,0,4" />
                        <TextBlock Text="• Comprehensive reporting" Margin="0,0,0,4" />
                        <TextBlock Text="• Export to Excel/CSV" Margin="0,0,0,4" />
                        <TextBlock Text="• Modern WPF interface with Material Design" />
                    </StackPanel>

                    <TextBlock Text="Standards Compliance:" FontWeight="SemiBold" Margin="0,0,0,8" />
                    <StackPanel Margin="16,0,0,0">
                        <TextBlock Text="• AS/NZS 3008 (Cable Selection)" Margin="0,0,0,4" />
                        <TextBlock Text="• AS/NZS 3000 (Wiring Rules)" Margin="0,0,0,4" />
                        <TextBlock Text="• IEC 60364 (Electrical Installations)" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Company Information -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock Text="Company Information" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <TextBlock Text="Beca" FontWeight="SemiBold" FontSize="16" Margin="0,0,0,8" />
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,16">
                        Beca is one of the largest independent engineering consultancies in the Asia-Pacific region, 
                        providing engineering, environmental, and planning services across multiple sectors.
                    </TextBlock>

                    <TextBlock Text="Contact Information:" FontWeight="SemiBold" Margin="0,0,0,8" />
                    <StackPanel Margin="16,0,0,0">
                        <TextBlock Text="Website: www.beca.com" Margin="0,0,0,4" />
                        <TextBlock Text="Email: <EMAIL>" Margin="0,0,0,4" />
                        <TextBlock Text="Support: For technical support, please contact your local Beca office" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Copyright -->
            <materialDesign:Card Padding="16">
                <StackPanel>
                    <TextBlock Text="Copyright and License" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="0,0,0,16" />

                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,8">
                        © 2024 Beca. All rights reserved.
                    </TextBlock>

                    <TextBlock TextWrapping="Wrap" FontSize="12" 
                               Foreground="{DynamicResource MaterialDesignBodyLight}">
                        This software is proprietary to Beca and is protected by copyright law. 
                        Unauthorized reproduction or distribution of this software, or any portion of it, 
                        may result in severe civil and criminal penalties.
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</Page>
