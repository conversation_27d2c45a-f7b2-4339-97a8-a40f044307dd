﻿<Page x:Class="MEP.PowerBIM_6.Views.PowerBIMCircuitEditPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
      mc:Ignorable="d" Background="White"
      Title="Circuit Editor">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
            <converters:PercentageConverter x:Key="PercentageConverter" />
        </ResourceDictionary>

    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <materialDesign:PackIcon Kind="ElectricSwitch" Width="24" Height="24" 
                                     VerticalAlignment="Center" Margin="0,0,8,0" />
            <TextBlock Text="Enhanced Circuit Editor" 
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}" 
                       VerticalAlignment="Center" />
        </StackPanel>

        <!-- Action Bar -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,16">
            <StackPanel Orientation="Horizontal">
                <Button Content="Calculate All" 
                  
                        Margin="0,0,8,0"
                        ToolTip="Run calculations for all circuits" />
                <Button Content="Save Changes" 
                   
                        Margin="0,0,8,0"
                        ToolTip="Save circuit modifications" />
                <Button Content="Reset Values" 
                   
                        Margin="0,0,8,0"
                        ToolTip="Reset to original values" />
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0" />
                <Button Content="Export to Excel" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,8,0"
                        ToolTip="Export circuit data to Excel" />
                <Button Content="Import from Excel" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,8,0"
                        ToolTip="Import circuit data from Excel" />
            </StackPanel>
        </materialDesign:Card>

        <!-- Circuit DataGrid -->
        <materialDesign:Card Grid.Row="2" Padding="8">
            <DataGrid x:Name="dgCircuits"
                     
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Extended"
                      GridLinesVisibility="All"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="{DynamicResource MaterialDesignDivider}"
                      RowBackground="Transparent">

                <DataGrid.Columns>
                    <!-- Circuit Number Column -->
                    <DataGridTextColumn Header="Circuit" 
                                        Binding="{Binding CCT_Number}" 
                                        Width="80" 
                                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                <Setter Property="FontWeight" Value="SemiBold" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- Description Column -->
                    <DataGridTextColumn Header="Description" 
                                        Binding="{Binding Schedule_Description}" 
                                        Width="200" 
                                        IsReadOnly="True" />

                    <!-- Current Column (Editable) -->
                    <DataGridTemplateColumn Header="Current (A)" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding EffectiveCurrent, StringFormat=F1}" 
                                               VerticalAlignment="Center"
                                               Visibility="{Binding ManualCurrent, Converter={StaticResource BoolToVisConverter}, ConverterParameter=True}" />
                                    <TextBox Text="{Binding Manual_PowerBim_User_Current, StringFormat=F1}" 
                                             Width="60"
                                             Visibility="{Binding ManualCurrent, Converter={StaticResource BoolToVisConverter}}" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Manual Current Toggle -->
                    <DataGridCheckBoxColumn Header="Manual" 
                                            Binding="{Binding ManualCurrent}" 
                                            Width="60"
                                            ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}" />

                    <!-- Poles Column -->
                    <DataGridTextColumn Header="Poles" 
                                        Binding="{Binding Number_Of_Poles}" 
                                        Width="60" />

                    <!-- Elements Column -->
                    <DataGridTextColumn Header="Elements" 
                                        Binding="{Binding Number_Of_Elements}" 
                                        Width="80" />

                    <!-- Cable To First Column -->
                    <DataGridTextColumn Header="Cable to First" 
                                        Binding="{Binding Schedule_Cable_To_First}" 
                                        Width="120" />

                    <!-- Cable To Final Column -->
                    <DataGridTextColumn Header="Cable to Final" 
                                        Binding="{Binding Schedule_Cable_To_Final}" 
                                        Width="120" />

                    <!-- Protection Device Column -->
                    <DataGridTextColumn Header="Protection" 
                                        Binding="{Binding Schedule_Protective_Device}" 
                                        Width="100" />

                    <!-- Trip Rating Column -->
                    <DataGridTextColumn Header="Trip (A)" 
                                        Binding="{Binding Schedule_Trip_Rating}" 
                                        Width="80" />

                    <!-- Circuit Type Column -->
                    <DataGridTextColumn Header="Type" 
                                        Binding="{Binding CircuitTypeDescription}" 
                                        Width="80" 
                                        IsReadOnly="True" />

                    <!-- Status Column -->
                    <DataGridTemplateColumn Header="Status" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="12" Padding="6,2" HorizontalAlignment="Center">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CheckResult}" Value="Pass">
                                                    <Setter Property="Background" Value="LightGreen" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CheckResult}" Value="Warning">
                                                    <Setter Property="Background" Value="LightGoldenrodYellow" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CheckResult}" Value="Fail">
                                                    <Setter Property="Background" Value="LightCoral" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding CheckResult}" 
                                               FontSize="10" 
                                               FontWeight="SemiBold" 
                                               HorizontalAlignment="Center" />
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Error Message Column -->
                    <DataGridTemplateColumn Header="Notes" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="View"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        FontSize="10"
                                        Padding="4,2"
                                        ToolTip="{Binding ErrorMessage}"
                                        Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <!-- Row Style for status indication -->
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding CheckResult}" Value="Fail">
                                <Setter Property="BorderBrush" Value="Red" />
                                <Setter Property="BorderThickness" Value="2,0,0,0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding CheckResult}" Value="Warning">
                                <Setter Property="BorderBrush" Value="Orange" />
                                <Setter Property="BorderThickness" Value="2,0,0,0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding CheckResult}" Value="Pass">
                                <Setter Property="BorderBrush" Value="Green" />
                                <Setter Property="BorderThickness" Value="2,0,0,0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding CCT_Is_Spare_Or_Space}" Value="True">
                                <Setter Property="Foreground" Value="Gray" />
                                <Setter Property="FontStyle" Value="Italic" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
