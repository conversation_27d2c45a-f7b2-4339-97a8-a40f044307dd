﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing cable data for WPF data binding
    /// </summary>
    public class PowerBIMCableDataModel : ObservableObject
    {
        #region Private Fields

        private string _cableName = string.Empty;
        private int _cableIndex;
        private bool _isCableToFirst = true;
        private double _spActive;
        private double _spEarth;
        private double _rRatedActive;
        private double _rRatedEarth;
        private double _rOperatingActive;
        private double _rOperatingEarth;
        private double _xMaxActive;
        private double _xMaxEarth;
        private double _zOperatingActive;
        private double _zOperatingEarth;
        private double _iRated;
        private double _temperature;
        private double _kValue;
        private double _i2tMax;
        private string _conductorMaterial = string.Empty;
        private string _insulationMaterial = string.Empty;
        private double _cableTemperatureLimit;
        private bool _dataGood = true;
        private string _errorMessage = string.Empty;
        private bool _warningUserDefCableSelected;
        private bool _warningEmLightingPresent;

        #endregion

        #region Constructor

        public PowerBIMCableDataModel()
        {
            DataGood = true;
        }

        #endregion

        #region Basic Properties

        /// <summary>
        /// Gets or sets the cable name
        /// </summary>
        [Required]
        public string CableName
        {
            get => _cableName;
            set => SetProperty(ref _cableName, value);
        }

        /// <summary>
        /// Gets or sets the cable index
        /// </summary>
        public int CableIndex
        {
            get => _cableIndex;
            set => SetProperty(ref _cableIndex, value);
        }

        /// <summary>
        /// Gets or sets whether this is cable to first element
        /// </summary>
        public bool IsCableToFirst
        {
            get => _isCableToFirst;
            set => SetProperty(ref _isCableToFirst, value);
        }

        #endregion

        #region Electrical Properties

        /// <summary>
        /// Gets or sets the active conductor cross-sectional area
        /// </summary>
        [Range(0, double.MaxValue)]
        public double SpActive
        {
            get => _spActive;
            set => SetProperty(ref _spActive, value);
        }

        /// <summary>
        /// Gets or sets the earth conductor cross-sectional area
        /// </summary>
        [Range(0, double.MaxValue)]
        public double SpEarth
        {
            get => _spEarth;
            set => SetProperty(ref _spEarth, value);
        }

        /// <summary>
        /// Gets or sets the rated active resistance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double RRatedActive
        {
            get => _rRatedActive;
            set => SetProperty(ref _rRatedActive, value);
        }

        /// <summary>
        /// Gets or sets the rated earth resistance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double RRatedEarth
        {
            get => _rRatedEarth;
            set => SetProperty(ref _rRatedEarth, value);
        }

        /// <summary>
        /// Gets or sets the operating active resistance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double ROperatingActive
        {
            get => _rOperatingActive;
            set => SetProperty(ref _rOperatingActive, value);
        }

        /// <summary>
        /// Gets or sets the operating earth resistance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double ROperatingEarth
        {
            get => _rOperatingEarth;
            set => SetProperty(ref _rOperatingEarth, value);
        }

        /// <summary>
        /// Gets or sets the maximum active reactance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double XMaxActive
        {
            get => _xMaxActive;
            set => SetProperty(ref _xMaxActive, value);
        }

        /// <summary>
        /// Gets or sets the maximum earth reactance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double XMaxEarth
        {
            get => _xMaxEarth;
            set => SetProperty(ref _xMaxEarth, value);
        }

        /// <summary>
        /// Gets or sets the operating active impedance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double ZOperatingActive
        {
            get => _zOperatingActive;
            set => SetProperty(ref _zOperatingActive, value);
        }

        /// <summary>
        /// Gets or sets the operating earth impedance
        /// </summary>
        [Range(0, double.MaxValue)]
        public double ZOperatingEarth
        {
            get => _zOperatingEarth;
            set => SetProperty(ref _zOperatingEarth, value);
        }

        /// <summary>
        /// Gets or sets the rated current
        /// </summary>
        [Range(0, double.MaxValue)]
        public double IRated
        {
            get => _iRated;
            set => SetProperty(ref _iRated, value);
        }

        /// <summary>
        /// Gets or sets the temperature
        /// </summary>
        [Range(-50, 200)]
        public double Temperature
        {
            get => _temperature;
            set => SetProperty(ref _temperature, value);
        }

        /// <summary>
        /// Gets or sets the K value
        /// </summary>
        [Range(0, double.MaxValue)]
        public double KValue
        {
            get => _kValue;
            set => SetProperty(ref _kValue, value);
        }

        /// <summary>
        /// Gets or sets the maximum I²t value
        /// </summary>
        [Range(0, double.MaxValue)]
        public double I2tMax
        {
            get => _i2tMax;
            set => SetProperty(ref _i2tMax, value);
        }

        #endregion

        #region Material Properties

        /// <summary>
        /// Gets or sets the conductor material
        /// </summary>
        public string ConductorMaterial
        {
            get => _conductorMaterial;
            set => SetProperty(ref _conductorMaterial, value);
        }

        /// <summary>
        /// Gets or sets the insulation material
        /// </summary>
        public string InsulationMaterial
        {
            get => _insulationMaterial;
            set => SetProperty(ref _insulationMaterial, value);
        }

        /// <summary>
        /// Gets or sets the cable temperature limit
        /// </summary>
        [Range(0, 300)]
        public double CableTemperatureLimit
        {
            get => _cableTemperatureLimit;
            set => SetProperty(ref _cableTemperatureLimit, value);
        }

        #endregion

        #region Status Properties

        /// <summary>
        /// Gets or sets whether the data is good
        /// </summary>
        public bool DataGood
        {
            get => _dataGood;
            set => SetProperty(ref _dataGood, value);
        }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets whether user-defined cable is selected warning
        /// </summary>
        public bool WarningUserDefCableSelected
        {
            get => _warningUserDefCableSelected;
            set => SetProperty(ref _warningUserDefCableSelected, value);
        }

        /// <summary>
        /// Gets or sets whether emergency lighting is present warning
        /// </summary>
        public bool WarningEmLightingPresent
        {
            get => _warningEmLightingPresent;
            set => SetProperty(ref _warningEmLightingPresent, value);
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets whether the cable is valid
        /// </summary>
        public bool IsValid => DataGood && !string.IsNullOrEmpty(CableName) && CableName != "Cannot Size Cable";

        /// <summary>
        /// Gets the cable description for display
        /// </summary>
        public string CableDescription
        {
            get
            {
                if (!IsValid) return "Invalid Cable";
                var description = CableName;
                if (SpActive > 0)
                    description += $" ({SpActive}mm²)";
                return description;
            }
        }

        /// <summary>
        /// Gets the status color for UI display
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (!DataGood) return "Red";
                if (WarningUserDefCableSelected || WarningEmLightingPresent) return "Orange";
                if (IsValid) return "Green";
                return "Gray";
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Creates a null/invalid cable entry
        /// </summary>
        public void CreateNullEntry()
        {
            CableName = "Cannot Size Cable";
            CableIndex = 0;
            SpActive = 0;
            SpEarth = 0;
            RRatedActive = 0;
            RRatedEarth = 0;
            ROperatingActive = 0;
            ROperatingEarth = 0;
            XMaxActive = 0;
            XMaxEarth = 0;
            ZOperatingActive = 0;
            ZOperatingEarth = 0;
            IRated = 0;
            Temperature = 0;
            KValue = 0;
            I2tMax = 0;
            ConductorMaterial = string.Empty;
            InsulationMaterial = string.Empty;
            CableTemperatureLimit = 0;
            DataGood = false;
            ErrorMessage = "Invalid Cable Selected";
            WarningUserDefCableSelected = false;
            WarningEmLightingPresent = false;
        }

        #endregion
    }
}
