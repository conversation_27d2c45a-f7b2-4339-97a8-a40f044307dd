﻿using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Service for PowerBIM electrical calculations
    /// </summary>
    public class PowerBIMCalculationService
    {
        private readonly UIApplication _uiApplication;
        private readonly BecaActivityLoggerData _logger;

        public PowerBIMCalculationService(UIApplication uiApplication, BecaActivityLoggerData logger)
        {
            _uiApplication = uiApplication;
            _logger = logger;
        }

        /// <summary>
        /// Runs the auto sizer for all selected distribution boards
        /// REVIT-SAFE: All operations run synchronously on main thread
        /// </summary>
        public bool RunAutoSizer(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Starting auto sizer...");
                _logger?.PostTaskEnd("Auto Sizer Started");

                var selectedDBs = distributionBoards.Where(db => db.IsSelected).ToList();
                if (!selectedDBs.Any())
                {
                    progress?.Report("No distribution boards selected");
                    return false;
                }

                int totalDBs = selectedDBs.Count;
                int processedDBs = 0;

                foreach (var db in selectedDBs)
                {
                    progress?.Report($"Processing {db.Schedule_DB_Name} ({processedDBs + 1}/{totalDBs})...");

                    // Process each circuit in the DB - now synchronous
                    ProcessDistributionBoard(db, projectInfo);

                    processedDBs++;
                    progress?.Report($"Completed {db.Schedule_DB_Name} ({processedDBs}/{totalDBs})");
                }

                progress?.Report("Auto sizer completed successfully");
                _logger?.PostTaskEnd("Auto Sizer Completed Successfully");
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"Auto sizer failed: {ex.Message}";
                progress?.Report(errorMsg);
                _logger?.PostTaskEnd(errorMsg);
                return false;
            }
        }

        /// <summary>
        /// Processes a single distribution board
        /// REVIT-SAFE: All operations run synchronously on main thread
        /// </summary>
        private void ProcessDistributionBoard(PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            try
            {
                // Reset counters
                db.Result_PassCount = 0;
                db.Result_WarningCount = 0;
                db.Result_FailCount = 0;

                // Process each circuit
                foreach (var circuit in db.Circuits)
                {
                    ProcessCircuit(circuit, db, projectInfo);
                }

                // Update DB status
                db.UpdateCircuitCounts();
                db.DB_All_Circuits_Pass = db.Result_FailCount == 0;

                // Generate summary
                db.DB_Result_Summary = GenerateDBSummary(db);

                // No artificial delays needed - Revit operations are naturally synchronous
            }
            catch (Exception ex)
            {
                db.GUI_Notes = $"Error processing DB: {ex.Message}";
                db.Data_Good = false;
            }
        }

        /// <summary>
        /// Processes a single circuit
        /// REVIT-SAFE: All operations run synchronously on main thread
        /// </summary>
        private void ProcessCircuit(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            try
            {
                // Skip spare/space circuits
                if (circuit.CCT_Is_Spare_Or_Space)
                {
                    circuit.CheckResult = "Pass";
                    return;
                }

                // Validate circuit data
                if (!ValidateCircuitData(circuit))
                {
                    circuit.CheckResult = "Fail";
                    circuit.ErrorMessage = "Invalid circuit data";
                    return;
                }

                // Run the PowerBIM calculation engine directly on main thread
                // CRITICAL: No Task.Run() - this ensures Revit API calls work properly
                circuit.RunPowerBIMCheck(db, projectInfo);

                // No artificial delays needed
            }
            catch (Exception ex)
            {
                circuit.CheckResult = "Fail";
                circuit.ErrorMessage = $"Calculation error: {ex.Message}";
            }
        }

        /// <summary>
        /// Validates circuit data
        /// </summary>
        private bool ValidateCircuitData(PowerBIMCircuitDataModel circuit)
        {
            if (string.IsNullOrEmpty(circuit.CCT_Number))
                return false;

            if (circuit.EffectiveCurrent <= 0)
                return false;

            if (circuit.Number_Of_Poles <= 0 || circuit.Number_Of_Poles > 3)
                return false;

            return true;
        }

        /// <summary>
        /// Calculates cable sizing
        /// </summary>
        private async Task<string> CalculateCableSizingAsync(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            // TODO: Implement actual cable sizing logic
            await Task.Delay(5);

            // Placeholder logic
            if (circuit.EffectiveCurrent > 100)
                return "Warning"; // Large current

            return "Pass";
        }

        /// <summary>
        /// Calculates breaker sizing
        /// </summary>
        private async Task<string> CalculateBreakerSizingAsync(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            // TODO: Implement actual breaker sizing logic
            await Task.Delay(5);

            // Placeholder logic
            if (circuit.Schedule_Trip_Rating <= 0)
                return "Fail"; // No breaker specified

            if (circuit.Schedule_Trip_Rating < circuit.EffectiveCurrent)
                return "Warning"; // Undersized breaker

            return "Pass";
        }

        /// <summary>
        /// Calculates voltage drop
        /// </summary>
        private async Task<string> CalculateVoltageDropAsync(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            // TODO: Implement actual voltage drop calculation
            await Task.Delay(5);

            // Placeholder logic
            var maxVD = projectInfo.SystemVDMaxPerc;
            var calculatedVD = 0.03; // Placeholder 3% VD

            if (calculatedVD > maxVD)
                return "Fail"; // Voltage drop too high

            if (calculatedVD > maxVD * 0.8)
                return "Warning"; // Close to limit

            return "Pass";
        }

        /// <summary>
        /// Determines the overall circuit result
        /// </summary>
        private string DetermineCircuitResult(string cableResult, string breakerResult, string voltageDropResult)
        {
            var results = new[] { cableResult, breakerResult, voltageDropResult };

            if (results.Any(r => r == "Fail"))
                return "Fail";

            if (results.Any(r => r == "Warning"))
                return "Warning";

            return "Pass";
        }

        /// <summary>
        /// Generates a summary for the distribution board
        /// </summary>
        private string GenerateDBSummary(PowerBIMDBDataModelEnhanced db)
        {
            var total = db.TotalCircuitCount;
            var pass = db.Result_PassCount;
            var warning = db.Result_WarningCount;
            var fail = db.Result_FailCount;

            return $"Total: {total}, Pass: {pass}, Warning: {warning}, Fail: {fail}";
        }

        /// <summary>
        /// Loads project data from Revit
        /// REVIT-SAFE: All Revit API calls run synchronously on main thread
        /// </summary>
        public bool LoadProjectData(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Loading project data...");
                _logger?.PostTaskEnd("Loading Project Data");

                // Load actual Revit data directly on main thread
                // CRITICAL: No Task.Run() - Revit API must be called from main thread
                LoadDistributionBoardsFromRevit(projectInfo, distributionBoards, progress);

                progress?.Report("Project data loaded successfully");
                _logger?.PostTaskEnd("Project Data Loaded Successfully");
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"Failed to load project data: {ex.Message}";
                progress?.Report(errorMsg);
                _logger?.PostTaskEnd(errorMsg);
                return false;
            }
        }

        /// <summary>
        /// Loads distribution boards from Revit document
        /// </summary>
        private void LoadDistributionBoardsFromRevit(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress = null)
        {
            distributionBoards.Clear();

            var document = projectInfo.Document;
            if (document == null)
            {
                _logger?.PostTaskEnd("No active document found");
                return;
            }

            progress?.Report("Finding electrical equipment...");

            // Find all electrical equipment (distribution boards/panels)
            var panels = new FilteredElementCollector(projectInfo.Document)
                .OfClass(typeof(PanelScheduleView)).Cast<PanelScheduleView>()
                .Select(p => document.GetElement(p.GetPanel())).ToList();

            progress?.Report($"Found {panels.Count} electrical equipment items");

            foreach (var panel in panels)
            {
                try
                {
                    var dbData = CreateDistributionBoardFromRevitElement(panel, document, projectInfo);
                    if (dbData != null)
                    {
                        distributionBoards.Add(dbData);
                        progress?.Report($"Loaded: {dbData.Schedule_DB_Name}");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.PostTaskEnd($"Error loading DB from element {panel.Id}: {ex.Message}");
                }
            }

            progress?.Report($"Loaded {distributionBoards.Count} distribution boards");

            // If no distribution boards found, create sample data for testing
            if (distributionBoards.Count == 0)
            {
                progress?.Report("No electrical equipment found, creating sample data...");
                CreateSampleData(distributionBoards);
            }
        }

        /// <summary>
        /// Creates sample data for testing (fallback)
        /// </summary>
        private void CreateSampleData(ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards)
        {
            distributionBoards.Clear();

            // Sample DB 1
            var db1 = new PowerBIMDBDataModelEnhanced
            {
                Schedule_DB_Name = "DB-01",
                Result_PassCount = 8,
                Result_WarningCount = 2,
                Result_FailCount = 1,
                GUI_Notes = "Sample distribution board",
                Data_Good = true
            };
            distributionBoards.Add(db1);

            // Sample DB 2
            var db2 = new PowerBIMDBDataModelEnhanced
            {
                Schedule_DB_Name = "DB-02",
                Result_PassCount = 12,
                Result_WarningCount = 0,
                Result_FailCount = 0,
                GUI_Notes = "All circuits pass",
                Data_Good = true
            };
            distributionBoards.Add(db2);

            // Sample DB 3
            var db3 = new PowerBIMDBDataModelEnhanced
            {
                Schedule_DB_Name = "DB-03",
                Result_PassCount = 5,
                Result_WarningCount = 3,
                Result_FailCount = 2,
                GUI_Notes = "Requires attention",
                Data_Good = true
            };
            distributionBoards.Add(db3);
        }

        /// <summary>
        /// Creates a PowerBIM distribution board model from a Revit electrical equipment element
        /// </summary>
        private PowerBIMDBDataModelEnhanced CreateDistributionBoardFromRevitElement(
            Element equipment,
            Document document,
            PowerBIMProjectInfoModel projectInfo)
        {
            try
            {
                // Get the element name/mark
                string dbName = equipment.Name;// GetElementName(equipment);
                if (string.IsNullOrEmpty(dbName))
                    return null;

                // Create the distribution board model
                var dbData = new PowerBIMDBDataModelEnhanced
                {
                    Schedule_DB_Name = dbName,
                    DB_Element = equipment,
                    DB_Location = GetElementLocation(equipment),
                    ProjectInfo = projectInfo,
                    Data_Good = true
                };

                // Get electrical system associated with this equipment
                var electricalSystem = GetElectricalSystemForEquipment(equipment, document);
                if (electricalSystem != null)
                {
                    dbData.DB_ElectricalSystem = electricalSystem;

                    // Load circuits from the electrical system
                    LoadCircuitsForDistributionBoard(dbData, electricalSystem, document);
                }

                // Get panel schedule view if it exists
                var panelScheduleView = GetPanelScheduleViewForEquipment(equipment, document);
                if (panelScheduleView != null)
                {
                    dbData.PanelScheduleView = panelScheduleView;

                    // Load additional data from panel schedule
                    LoadDataFromPanelSchedule(dbData, panelScheduleView);
                }

                // Load equipment parameters
                LoadEquipmentParameters(dbData, equipment);

                // Set initial status
                dbData.GUI_Notes = $"Loaded from Revit element {equipment.Id}";

                return dbData;
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error creating DB from element {equipment.Id}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Runs the complete PowerBIM calculation engine for a circuit
        /// </summary>
        private void RunPowerBIMCalculationEngine(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            // Determine clearing time
            circuit.ClearingTime = PowerBIMCalculations.DetermineClearingTime(circuit, projectInfo);

            // Calculate diversified load
            circuit.DiversifiedCurrent = PowerBIMCalculations.CalculateDiversifiedCircuitLoad(circuit, projectInfo);

            // Create cable data models for calculations (simplified - would need cable database)
            var cableToFirst = CreateCableDataModel(circuit.Schedule_Cable_To_First);
            var cableToFinal = CreateCableDataModel(circuit.Schedule_Cable_To_Final);

            // Calculate voltage drop
            if (cableToFirst != null && cableToFinal != null)
            {
                bool useLinearDepreciating = circuit.CCT_Is_Lighting ?
                    projectInfo.LightingVDCalculation == VoltDropCalculation.LinearDeprecating :
                    projectInfo.PowerVDCalculation == VoltDropCalculation.LinearDeprecating;
                double voltageDropVolts = PowerBIMCalculations.CalculateFinalCircuitVoltDrop(circuit, cableToFirst, cableToFinal, useLinearDepreciating);
                circuit.VoltageDropPercentage = PowerBIMCalculations.CalculateFinalCircuitVoltDropPercentage(voltageDropVolts, circuit.Number_Of_Poles);
            }

            // Calculate EFLI (simplified)
            var breaker = CreateBreakerDataModel(circuit);
            if (breaker != null)
            {
                PowerBIMCalculations.CheckEFLI(circuit, breaker, db.EFLI_R, db.EFLI_X);
            }
        }

        /// <summary>
        /// Gets the name/mark of a Revit element
        /// </summary>
        private string GetElementName(Element element)
        {
            // Try to get the Mark parameter first (most common for electrical equipment)
            var markParam = element.get_Parameter(BuiltInParameter.ALL_MODEL_MARK);
            if (markParam != null && !string.IsNullOrEmpty(markParam.AsString()))
                return markParam.AsString();

            // Fallback to Name parameter
            var nameParam = element.get_Parameter(BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS);
            if (nameParam != null && !string.IsNullOrEmpty(nameParam.AsString()))
                return nameParam.AsString();

            // Fallback to element name
            return element.Name ?? $"DB_{element.Id}";
        }

        /// <summary>
        /// Gets the location of a Revit element
        /// </summary>
        private XYZ GetElementLocation(Element element)
        {
            if (element.Location is LocationPoint locationPoint)
                return locationPoint.Point;

            if (element.Location is LocationCurve locationCurve)
                return locationCurve.Curve.GetEndPoint(0);

            return XYZ.Zero;
        }

        /// <summary>
        /// Gets the electrical system associated with equipment
        /// </summary>
        private ElectricalSystem GetElectricalSystemForEquipment(Element equipment, Document document)
        {
            try
            {
                // Find electrical systems that reference this equipment
                var electricalSystems = new FilteredElementCollector(document)
                    .OfClass(typeof(ElectricalSystem))
                    .Cast<ElectricalSystem>()
                    .Where(es => es.BaseEquipment?.Id == equipment.Id)
                    .FirstOrDefault();

                return electricalSystems;
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error finding electrical system for equipment {equipment.Id}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a cable data model from cable name (simplified)
        /// </summary>
        private PowerBIMCableDataModel CreateCableDataModel(string cableName)
        {
            if (string.IsNullOrEmpty(cableName) || cableName == "Cannot Size Cable")
                return null;

            // Simplified cable data - in full implementation would lookup from cable database
            return new PowerBIMCableDataModel
            {
                CableName = cableName,
                ZOperatingActive = 18.1, // Placeholder value for 2.5mm² cable
                DataGood = true
            };
        }

        /// <summary>
        /// Gets the panel schedule view for equipment
        /// </summary>
        private PanelScheduleView GetPanelScheduleViewForEquipment(Element equipment, Document document)
        {
            try
            {
                // Find panel schedule views that reference this equipment
                var panelScheduleViews = new FilteredElementCollector(document)
                    .OfClass(typeof(PanelScheduleView))
                    .Cast<PanelScheduleView>()
                    .Where(psv => psv.GetPanel() == equipment.Id)
                    .FirstOrDefault();

                return panelScheduleViews;
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error finding panel schedule for equipment {equipment.Id}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Loads circuits for a distribution board from electrical system
        /// </summary>
        private void LoadCircuitsForDistributionBoard(PowerBIMDBDataModelEnhanced dbData, ElectricalSystem electricalSystem, Document document)
        {
            try
            {
                dbData.Circuits.Clear();

                // Get all circuits connected to this electrical system
                var elementIds = electricalSystem.Elements.Cast<ElementId>();
                var circuits = elementIds
                    .Select(id => document.GetElement(id))
                    .OfType<ElectricalSystem>()
                    .Where(es => es.SystemType == ElectricalSystemType.PowerCircuit);

                foreach (var circuit in circuits)
                {
                    var circuitData = CreateCircuitFromElectricalSystem(circuit, dbData);
                    if (circuitData != null)
                    {
                        dbData.Circuits.Add(circuitData);
                    }
                }

                // Update circuit counts
                dbData.UpdateCircuitCounts();
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error loading circuits for DB {dbData.Schedule_DB_Name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a circuit data model from an electrical system
        /// </summary>
        private PowerBIMCircuitDataModel CreateCircuitFromElectricalSystem(ElectricalSystem electricalSystem, PowerBIMDBDataModelEnhanced parentDB)
        {
            try
            {
                var circuit = new PowerBIMCircuitDataModel
                {
                    CCT_Electrical_System = electricalSystem,
                    ParentDB = parentDB,
                    ProjectInfo = parentDB.ProjectInfo
                };

                // Load circuit parameters from electrical system
                LoadCircuitParameters(circuit, electricalSystem);

                // Set the circuit element reference if available
                var elementIds = electricalSystem.Elements.Cast<ElementId>();
                if (elementIds.Any())
                {
                    var firstElement = parentDB.ProjectInfo.Document.GetElement(elementIds.First());
                    circuit.CCT_Element = firstElement;
                }

                return circuit;
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error creating circuit from electrical system {electricalSystem.Id}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a breaker data model from circuit (simplified)
        /// </summary>
        private PowerBIMBreakerDataModel CreateBreakerDataModel(PowerBIMCircuitDataModel circuit)
        {
            if (string.IsNullOrEmpty(circuit.Schedule_Protective_Device))
                return null;

            // Simplified breaker data - in full implementation would lookup from breaker database
            return new PowerBIMBreakerDataModel
            {
                BreakerName = circuit.Schedule_Protective_Device,
                TripRating = circuit.Schedule_Trip_Rating,
                CurveType = circuit.Schedule_Curve_Type,
                EFLI_04 = 1.0, // Placeholder values
                EFLI_50 = 2.0,
                DataGood = true
            };
        }

        /// <summary>
        /// Loads equipment parameters from Revit element
        /// </summary>
        private void LoadEquipmentParameters(PowerBIMDBDataModelEnhanced dbData, Element equipment)
        {
            try
            {
                // Load common electrical equipment parameters
                dbData.UpstreamDeviceRating = GetParameterValueAsDouble(equipment, "Upstream Device Rating") ?? 0.0;
                dbData.DeviceKARating = GetParameterValueAsDouble(equipment, "Device kA Rating") ?? 0.0;
                dbData.EFLI_R = GetParameterValueAsDouble(equipment, "EFLI R") ?? 0.0;
                dbData.EFLI_X = GetParameterValueAsDouble(equipment, "EFLI X") ?? 0.0;
                dbData.DBVD = GetParameterValueAsDouble(equipment, "DB VD") ?? 0.0;
                dbData.PSCC = GetParameterValueAsDouble(equipment, "PSCC") ?? 0.0;
                dbData.NumberOfWays = (int)(GetParameterValueAsDouble(equipment, "Number of Ways") ?? 0.0);

                // Load text parameters
                dbData.CircuitNaming = GetParameterValueAsString(equipment, "Circuit Naming") ?? "";
                dbData.User_Notes = GetParameterValueAsString(equipment, "Beca Inst Use") ?? "";
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error loading equipment parameters for {dbData.Schedule_DB_Name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads circuit parameters from electrical system
        /// </summary>
        private void LoadCircuitParameters(PowerBIMCircuitDataModel circuit, ElectricalSystem electricalSystem)
        {
            try
            {
                // Get circuit name/number
                circuit.CCT_Number = GetParameterValueAsString(electricalSystem, "Circuit Number") ??
                                    GetParameterValueAsString(electricalSystem, "Panel") ??
                                    electricalSystem.Name;

                // Load electrical parameters from Revit
                circuit.Revit_Current = GetParameterValueAsDouble(electricalSystem, "Current") ?? 0.0;
                circuit.Number_Of_Poles = (int)(GetParameterValueAsDouble(electricalSystem, "Number of Poles") ?? 1.0);
                circuit.Number_Of_Elements = (int)(GetParameterValueAsDouble(electricalSystem, "Number of Elements") ?? 1.0);

                // Load schedule parameters (these would come from panel schedule if available)
                circuit.Schedule_Description = GetParameterValueAsString(electricalSystem, "Description") ?? "";
                circuit.Schedule_Protective_Device = GetParameterValueAsString(electricalSystem, "Protective Device") ?? "";
                circuit.Schedule_Trip_Rating = GetParameterValueAsDouble(electricalSystem, "Trip Rating") ?? 0.0;
                circuit.Schedule_Curve_Type = GetParameterValueAsString(electricalSystem, "Curve Type") ?? "";
                circuit.Schedule_Cable_To_First = GetParameterValueAsString(electricalSystem, "Cable To First") ?? "";
                circuit.Schedule_Cable_To_Final = GetParameterValueAsString(electricalSystem, "Cable To Final") ?? "";

                // Load circuit type flags
                string circuitType = GetParameterValueAsString(electricalSystem, "Circuit Type") ?? "";
                circuit.CCT_Is_Lighting = circuitType.ToLower().Contains("lighting");
                circuit.CCT_Is_Power = circuitType.ToLower().Contains("power") || string.IsNullOrEmpty(circuitType);
                circuit.CCT_Is_Spare_Or_Space = circuitType.ToLower().Contains("spare") || circuitType.ToLower().Contains("space");

                // Load additional circuit properties
                circuit.CCT_GPO_Present = GetParameterValueAsString(electricalSystem, "GPO Present")?.ToLower() == "true";
                circuit.GPO_Count = (int)(GetParameterValueAsDouble(electricalSystem, "GPO Count") ?? 0.0);
                circuit.CCT_RCD_ElementIsPresent = GetParameterValueAsString(electricalSystem, "RCD Present")?.ToLower() == "true";
                circuit.CCT_RCD_Name = GetParameterValueAsString(electricalSystem, "RCD Name") ?? "";

                // Set initial status
                circuit.DataGood = true;
                circuit.CheckResult = "Not Checked";
                circuit.ParametersGood = true;
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error loading circuit parameters for {electricalSystem.Name}: {ex.Message}");
                circuit.DataGood = false;
                circuit.ParametersGood = false;
                circuit.ErrorMessage = $"Parameter loading error: {ex.Message}";
            }
        }

        /// <summary>
        /// Loads additional data from panel schedule view
        /// </summary>
        private void LoadDataFromPanelSchedule(PowerBIMDBDataModelEnhanced dbData, PanelScheduleView panelScheduleView)
        {
            try
            {
                // Panel schedule views contain detailed circuit information
                // This would require more complex implementation to read the schedule data
                _logger?.PostTaskEnd($"Panel schedule found for {dbData.Schedule_DB_Name}");
            }
            catch (Exception ex)
            {
                _logger?.PostTaskEnd($"Error loading panel schedule data for {dbData.Schedule_DB_Name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets a parameter value as double
        /// </summary>
        private double? GetParameterValueAsDouble(Element element, string parameterName)
        {
            try
            {
                var param = element.LookupParameter(parameterName);
                if (param != null && param.HasValue)
                {
                    return param.AsDouble();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Gets a parameter value as string
        /// </summary>
        private string GetParameterValueAsString(Element element, string parameterName)
        {
            try
            {
                var param = element.LookupParameter(parameterName);
                if (param != null && param.HasValue)
                {
                    return param.AsString();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Determines if circuit result should be warning or failure
        /// </summary>
        private string DetermineCircuitResultSeverity(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            // Critical failures
            if (circuit.EffectiveCurrent > circuit.Schedule_Trip_Rating * 1.2) // 20% over breaker rating
                return "Fail";

            if (circuit.VoltageDropPercentage > (projectInfo.IsSystemVD5Percent ? 0.08 : 0.10)) // Significantly over VD limit
                return "Fail";

            // Everything else is a warning
            return "Warning";
        }
    }
}
