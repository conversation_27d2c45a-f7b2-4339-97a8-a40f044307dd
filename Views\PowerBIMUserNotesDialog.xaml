﻿<Window x:Class="MEP.PowerBIM_6.Views.PowerBIMUserNotesDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="User Notes" Height="400" Width="500"  WindowStartupLocation="CenterOwner"
    ResizeMode="CanResize" Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="16,12">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="NoteText" Width="24" Height="24" 
                                         Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0" />
                <TextBlock Text="{Binding DBName, StringFormat='User Notes for {0}'}" 
                           Foreground="White" 
                           FontSize="16" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center" />
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Instructions -->
            <TextBlock Grid.Row="0" 
                       Text="Add your notes for this distribution board. These notes will be saved to the Revit element." 
                       TextWrapping="Wrap" 
                       Margin="0,0,0,16" 
                       Foreground="{DynamicResource MaterialDesignBodyLight}" />

            <!-- Notes Text Box -->
            <Border Grid.Row="1" 
                    BorderBrush="{DynamicResource MaterialDesignDivider}" 
                    BorderThickness="1" 
                    CornerRadius="4">
                <TextBox x:Name="txtNotes"
                         Text="{Binding UserNotes, UpdateSourceTrigger=PropertyChanged}"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         Padding="8"
                         BorderThickness="0"
                         Background="Transparent"
                         FontFamily="Segoe UI"
                         FontSize="12" />
            </Border>

            <!-- Character Count -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                <TextBlock Text="Characters: " 
                           Foreground="{DynamicResource MaterialDesignBodyLight}" 
                           FontSize="10" />
                <TextBlock Text="{Binding ElementName=txtNotes, Path=Text.Length}" 
                           Foreground="{DynamicResource MaterialDesignBodyLight}" 
                           FontSize="10" 
                           FontWeight="SemiBold" />
            </StackPanel>

            <!-- Button Panel -->
            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}" Padding="16">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="Clear All" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Click="ClearAll_Click"
                        Margin="0,0,8,0"
                        ToolTip="Clear all notes" />
                    <Button Content="Save" 
                   
                        Click="Save_Click"
                        Margin="0,0,8,0"
                        IsDefault="True" />
                    <Button Content="Cancel" 
                     
                        Click="Cancel_Click"
                        IsCancel="True" />
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
