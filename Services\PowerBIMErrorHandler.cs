﻿using BecaActivityLogger.CoreLogic.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Centralized error handling and logging service for PowerBIM
    /// </summary>
    public class PowerBIMErrorHandler
    {
        #region Private Fields

        private readonly BecaActivityLoggerData _logger;
        private readonly List<ErrorRecord> _errorHistory = new List<ErrorRecord>();

        #endregion

        #region Constructor

        public PowerBIMErrorHandler(BecaActivityLoggerData logger = null)
        {
            _logger = logger;
        }

        #endregion

        #region Error Handling

        /// <summary>
        /// Handles an exception with logging and user notification
        /// </summary>
        public void HandleException(Exception ex, string context = null, bool showToUser = true)
        {
            var errorRecord = new ErrorRecord
            {
                Exception = ex,
                Context = context ?? "Unknown",
                Timestamp = DateTime.Now,
                Severity = GetSeverity(ex)
            };

            _errorHistory.Add(errorRecord);

            // Log to debug output
            Debug.WriteLine($"[ERROR] {errorRecord.Timestamp:HH:mm:ss} - {errorRecord.Context}: {ex.Message}");
            Debug.WriteLine($"[ERROR] Stack Trace: {ex.StackTrace}");

            // Log to Beca Activity Logger if available
            _logger?.PostTaskEnd($"Error in {context}: {ex.Message}");

            // Log to file
            LogToFile(errorRecord);

            // Show to user if requested
            if (showToUser)
            {
                ShowErrorToUser(errorRecord);
            }
        }

        /// <summary>
        /// Handles a warning message
        /// </summary>
        public void HandleWarning(string message, string context = null, bool showToUser = false)
        {
            var warningRecord = new ErrorRecord
            {
                Message = message,
                Context = context ?? "Unknown",
                Timestamp = DateTime.Now,
                Severity = ErrorSeverity.Warning
            };

            _errorHistory.Add(warningRecord);

            // Log to debug output
            Debug.WriteLine($"[WARNING] {warningRecord.Timestamp:HH:mm:ss} - {warningRecord.Context}: {message}");

            // Log to Beca Activity Logger if available
            _logger?.PostTaskEnd($"Warning in {context}: {message}");

            // Log to file
            LogToFile(warningRecord);

            // Show to user if requested
            if (showToUser)
            {
                ShowWarningToUser(warningRecord);
            }
        }

        /// <summary>
        /// Handles an informational message
        /// </summary>
        public void HandleInfo(string message, string context = null)
        {
            var infoRecord = new ErrorRecord
            {
                Message = message,
                Context = context ?? "Unknown",
                Timestamp = DateTime.Now,
                Severity = ErrorSeverity.Information
            };

            _errorHistory.Add(infoRecord);

            // Log to debug output
            Debug.WriteLine($"[INFO] {infoRecord.Timestamp:HH:mm:ss} - {infoRecord.Context}: {message}");

            // Log to Beca Activity Logger if available
            _logger?.PostTaskEnd($"Info: {message}");
        }

        #endregion

        #region User Notification

        private void ShowErrorToUser(ErrorRecord errorRecord)
        {
            try
            {
                var message = $"An error occurred in {errorRecord.Context}:\n\n{errorRecord.Exception?.Message ?? errorRecord.Message}";

                if (errorRecord.Exception is OutOfMemoryException)
                {
                    message += "\n\nThis appears to be a memory issue. Try closing other applications and working with smaller datasets.";
                }
                else if (errorRecord.Exception is UnauthorizedAccessException)
                {
                    message += "\n\nThis appears to be a permissions issue. Check that you have access to the required files and folders.";
                }
                else if (errorRecord.Exception is FileNotFoundException)
                {
                    message += "\n\nA required file could not be found. Check that all necessary files are in place.";
                }

                Views.PowerBIMMessageDialog.ShowError(message, "PowerBIM Error");
            }
            catch (Exception ex)
            {
                // Fallback to system message box if our custom dialog fails
                MessageBox.Show($"Error: {errorRecord.Exception?.Message ?? errorRecord.Message}",
                               "PowerBIM Error",
                               MessageBoxButton.OK,
                               MessageBoxImage.Error);

                Debug.WriteLine($"Failed to show custom error dialog: {ex.Message}");
            }
        }

        private void ShowWarningToUser(ErrorRecord warningRecord)
        {
            try
            {
                var message = $"Warning in {warningRecord.Context}:\n\n{warningRecord.Message}";
                Views.PowerBIMMessageDialog.ShowWarning(message, "PowerBIM Warning");
            }
            catch (Exception ex)
            {
                // Fallback to system message box
                MessageBox.Show(warningRecord.Message,
                               "PowerBIM Warning",
                               MessageBoxButton.OK,
                               MessageBoxImage.Warning);

                Debug.WriteLine($"Failed to show custom warning dialog: {ex.Message}");
            }
        }

        #endregion

        #region File Logging

        private void LogToFile(ErrorRecord errorRecord)
        {
            try
            {
                var logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                                               "Beca", "PowerBIM", "Logs");

                if (!Directory.Exists(logDirectory))
                    Directory.CreateDirectory(logDirectory);

                var logFile = Path.Combine(logDirectory, $"PowerBIM_{DateTime.Now:yyyy-MM-dd}.log");

                var logEntry = $"[{errorRecord.Timestamp:yyyy-MM-dd HH:mm:ss}] [{errorRecord.Severity}] [{errorRecord.Context}] ";

                if (errorRecord.Exception != null)
                {
                    logEntry += $"{errorRecord.Exception.Message}\n{errorRecord.Exception.StackTrace}";
                }
                else
                {
                    logEntry += errorRecord.Message;
                }

                logEntry += Environment.NewLine + Environment.NewLine;

                File.AppendAllText(logFile, logEntry);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to log to file: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private ErrorSeverity GetSeverity(Exception ex)
        {
            return ex switch
            {
                OutOfMemoryException => ErrorSeverity.Critical,
                UnauthorizedAccessException => ErrorSeverity.Error,
                FileNotFoundException => ErrorSeverity.Error,
                DirectoryNotFoundException => ErrorSeverity.Error,
                ArgumentNullException => ErrorSeverity.Error,
                InvalidOperationException => ErrorSeverity.Warning,
                NotImplementedException => ErrorSeverity.Warning,
                _ => ErrorSeverity.Error
            };
        }

        /// <summary>
        /// Gets the error history
        /// </summary>
        public List<ErrorRecord> GetErrorHistory()
        {
            return new List<ErrorRecord>(_errorHistory);
        }

        /// <summary>
        /// Clears the error history
        /// </summary>
        public void ClearHistory()
        {
            _errorHistory.Clear();
        }

        /// <summary>
        /// Gets error statistics
        /// </summary>
        public ErrorStatistics GetStatistics()
        {
            return new ErrorStatistics
            {
                TotalErrors = _errorHistory.Count,
                CriticalErrors = _errorHistory.Count(e => e.Severity == ErrorSeverity.Critical),
                Errors = _errorHistory.Count(e => e.Severity == ErrorSeverity.Error),
                Warnings = _errorHistory.Count(e => e.Severity == ErrorSeverity.Warning),
                Information = _errorHistory.Count(e => e.Severity == ErrorSeverity.Information),
                MostRecentError = _errorHistory.LastOrDefault()?.Timestamp,
                MostCommonContext = _errorHistory.GroupBy(e => e.Context)
                                                 .OrderByDescending(g => g.Count())
                                                 .FirstOrDefault()?.Key
            };
        }

        #endregion

        #region Nested Classes

        public class ErrorRecord
        {
            public Exception Exception { get; set; }
            public string Message { get; set; }
            public string Context { get; set; }
            public DateTime Timestamp { get; set; }
            public ErrorSeverity Severity { get; set; }
        }

        public enum ErrorSeverity
        {
            Information,
            Warning,
            Error,
            Critical
        }

        public class ErrorStatistics
        {
            public int TotalErrors { get; set; }
            public int CriticalErrors { get; set; }
            public int Errors { get; set; }
            public int Warnings { get; set; }
            public int Information { get; set; }
            public DateTime? MostRecentError { get; set; }
            public string MostCommonContext { get; set; }
        }

        #endregion

        #region Static Instance

        private static readonly Lazy<PowerBIMErrorHandler> _instance =
            new Lazy<PowerBIMErrorHandler>(() => new PowerBIMErrorHandler());

        public static PowerBIMErrorHandler Instance => _instance.Value;

        #endregion
    }
}
