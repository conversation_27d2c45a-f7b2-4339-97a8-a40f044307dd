﻿<Page x:Class="MEP.PowerBIM_6.Views.PowerBIMCircuitsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" Background="White"
      Title="Circuits">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Page.Resources>

    <Grid>
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <materialDesign:PackIcon Kind="ElectricSwitch" Width="64" Height="64" 
                                     HorizontalAlignment="Center" Margin="0,0,0,16" />
            <TextBlock Text="Circuits Page" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,8" />
            <TextBlock Text="Circuit details and analysis will be displayed here." 
                       HorizontalAlignment="Center" 
                       TextAlignment="Center" 
                       Foreground="{DynamicResource MaterialDesignBodyLight}" />
            <TextBlock Text="This page will be implemented in Phase 4." 
                       HorizontalAlignment="Center" 
                       TextAlignment="Center" 
                       FontStyle="Italic" 
                       Margin="0,16,0,0" />
        </StackPanel>
    </Grid>
</Page>
