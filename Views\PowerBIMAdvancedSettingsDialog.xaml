﻿<Window x:Class="MEP.PowerBIM_6.Views.PowerBIMAdvancedSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
        mc:Ignorable="d"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
        Title="Advanced Settings" Height="600" Width="700">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
        </ResourceDictionary>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="16,12">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="SettingsBox" Width="24" Height="24" 
                                         Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0" />
                <TextBlock Text="PowerBIM Advanced Settings" 
                           Foreground="White" 
                           FontSize="16" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center" />
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
            <StackPanel>
                <!-- GPO Calculation Settings -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="GPO Calculation Settings" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                   Margin="0,0,0,16" />

                        <TextBlock Text="Select how GPO (General Power Outlet) circuits should be calculated:" 
                                   TextWrapping="Wrap" 
                                   Margin="0,0,0,12" 
                                   Foreground="{DynamicResource MaterialDesignBodyLight}" />

                        <StackPanel>
                            <RadioButton Content="Calculate at 80% of breaker rating" 
                                         IsChecked="{Binding GPOCalc80Percent}" 
                                         Margin="0,0,0,8" />
                            <RadioButton Content="Use actual assigned load" 
                                         IsChecked="{Binding GPOActualLoad}" 
                                         Margin="0,0,0,8" />
                            <RadioButton Content="1000W + 100W per additional outlet" 
                                         IsChecked="{Binding GPO1000Plus100}" />
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Clearing Time Settings -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="Clearing Time Settings" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                   Margin="0,0,0,16" />

                        <!-- Power Circuits -->
                        <GroupBox Header="Power Circuits" Margin="0,0,0,16">
                            <StackPanel Margin="8">
                                <RadioButton Content="0.4 seconds clearing time" 
                                             IsChecked="{Binding ClearingTimePower04}" 
                                             Margin="0,0,0,8" />
                                <RadioButton Content="5 seconds clearing time" 
                                             IsChecked="{Binding ClearingTimePower5}" />
                            </StackPanel>
                        </GroupBox>

                        <!-- Lighting Circuits -->
                        <GroupBox Header="Lighting Circuits">
                            <StackPanel Margin="8">
                                <RadioButton Content="0.4 seconds clearing time" 
                                             IsChecked="{Binding ClearingTimeLighting04}" 
                                             Margin="0,0,0,8" />
                                <RadioButton Content="5 seconds clearing time" 
                                             IsChecked="{Binding ClearingTimeLighting5}" />
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Database Settings -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="Cable Database Settings" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                   Margin="0,0,0,16" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="Database Path:" 
                                       VerticalAlignment="Center" Margin="0,0,8,0" />
                            <TextBox Grid.Column="1" 
                                     Text="{Binding DatabasePath}" 
                                     IsReadOnly="True"
                                     Background="{DynamicResource MaterialDesignDivider}"
                                     Margin="0,0,8,0" />
                            <Button Grid.Column="2" Content="Browse..." 
                                   
                                    Click="BrowseDatabase_Click" />
                        </Grid>

                        <TextBlock Text="Current database status and information will be displayed here." 
                                   Margin="0,16,0,0" 
                                   Foreground="{DynamicResource MaterialDesignBodyLight}" 
                                   FontStyle="Italic" />
                    </StackPanel>
                </materialDesign:Card>

                <!-- Calculation Parameters -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="Calculation Parameters" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                   Margin="0,0,0,16" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Discrimination Test Multiplier:" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                     Text="{Binding DiscriminationTestMultiplier, Converter={StaticResource NumericFormatConverter}}" 
                                     Margin="0,0,16,8" />

                            <TextBlock Grid.Row="0" Grid.Column="2" Text="Ambient Temperature (°C):" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="0" Grid.Column="3" 
                                     Text="{Binding AmbientTemperature, Converter={StaticResource NumericFormatConverter}}" 
                                     Margin="0,0,0,8" />

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Node Circuit Path Tolerance (mm):" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="1" Grid.Column="1" 
                                     Text="{Binding NodeCircuitPathTolerance, Converter={StaticResource NumericFormatConverter}}" 
                                     Margin="0,0,16,8" />

                            <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="4" 
                                      Content="Enable advanced path finding algorithms" 
                                      IsChecked="{Binding EnableAdvancedPathFinding}" 
                                      Margin="0,8,0,0" />
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Safety Factors -->
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <TextBlock Text="Safety Factors" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                   Margin="0,0,0,16" />

                        <TextBlock Text="Configure safety factors for different circuit types:" 
                                   TextWrapping="Wrap" 
                                   Margin="0,0,0,12" 
                                   Foreground="{DynamicResource MaterialDesignBodyLight}" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Power Circuits:" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                     Text="{Binding PowerSafetyFactor, Converter={StaticResource NumericFormatConverter}}" 
                                     Margin="0,0,16,8" />

                            <TextBlock Grid.Row="0" Grid.Column="2" Text="Lighting Circuits:" 
                                       VerticalAlignment="Center" Margin="0,0,8,8" />
                            <TextBox Grid.Row="0" Grid.Column="3" 
                                     Text="{Binding LightingSafetyFactor, Converter={StaticResource NumericFormatConverter}}" 
                                     Margin="0,0,0,8" />

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Other Circuits:" 
                                       VerticalAlignment="Center" Margin="0,0,8,0" />
                            <TextBox Grid.Row="1" Grid.Column="1" 
                                     Text="{Binding OtherSafetyFactor, Converter={StaticResource NumericFormatConverter}}" 
                                     Margin="0,0,16,0" />
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Button Panel -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}" Padding="16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="Reset to Defaults" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Click="ResetDefaults_Click"
                        Margin="0,0,8,0" />
                <Button Content="Save" 
                   
                        Click="Save_Click"
                        Margin="0,0,8,0"
                        IsDefault="True" />
                <Button Content="Cancel" 
                 
                        Click="Cancel_Click"
                        IsCancel="True" />
            </StackPanel>
        </Border>
    </Grid>
</Window>
