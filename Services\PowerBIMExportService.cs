﻿using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Export service for PowerBIM data - Excel and CSV export functionality
    /// Ported from the original PowerBIM_ExcelExport and PowerBIM_CSVExport classes
    /// </summary>
    public class PowerBIMExportService
    {
        #region Excel Export

        /// <summary>
        /// Exports PowerBIM data to Excel format
        /// </summary>
        public async Task<bool> ExportToExcelAsync(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            string filePath,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Starting Excel export...");

                // Create Excel content
                var excelContent = await Task.Run(() => GenerateExcelContent(projectInfo, distributionBoards, progress));

                // Write to file
                //await File.WriteAllTextAsync(filePath, excelContent); // for .NET Core /.NET 5+
                await Task.Run(() => File.WriteAllText(filePath, excelContent));

                progress?.Report("Excel export completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                progress?.Report($"Excel export failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Generates Excel content (simplified - would use actual Excel library in production)
        /// </summary>
        private string GenerateExcelContent(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress)
        {
            var content = new StringBuilder();

            // Project Information Sheet
            content.AppendLine("PowerBIM Export Report");
            content.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            content.AppendLine();

            // Project Info
            content.AppendLine("PROJECT INFORMATION");
            content.AppendLine($"Job Name,{projectInfo.JobName}");
            content.AppendLine($"Job Number,{projectInfo.JobNumber}");
            content.AppendLine($"Engineer,{projectInfo.Engineer}");
            content.AppendLine($"Verifier,{projectInfo.Verifier}");
            content.AppendLine($"Revision,{projectInfo.Revision}");
            content.AppendLine();

            // Distribution Boards Summary
            content.AppendLine("DISTRIBUTION BOARDS SUMMARY");
            content.AppendLine("DB Name,Total Circuits,Pass,Warning,Fail,Status");

            foreach (var db in distributionBoards.Where(db => db.IsSelected))
            {
                progress?.Report($"Processing DB: {db.Schedule_DB_Name}");

                content.AppendLine($"{db.Schedule_DB_Name},{db.TotalCircuitCount},{db.Result_PassCount},{db.Result_WarningCount},{db.Result_FailCount},{(db.AllCircuitsPass ? "PASS" : "FAIL")}");
            }

            content.AppendLine();

            // Detailed Circuit Information
            content.AppendLine("DETAILED CIRCUIT INFORMATION");
            content.AppendLine("DB Name,Circuit,Description,Poles,Current,Trip Rating,Cable To First,Cable To Final,Voltage Drop %,EFLI,Result,Error Message");

            foreach (var db in distributionBoards.Where(db => db.IsSelected))
            {
                if (db.Circuits != null)
                {
                    foreach (var circuit in db.Circuits)
                    {
                        content.AppendLine($"{db.Schedule_DB_Name},{circuit.CCT_Number},{circuit.Schedule_Description},{circuit.Number_Of_Poles},{circuit.EffectiveCurrent:F2},{circuit.Schedule_Trip_Rating},{circuit.Schedule_Cable_To_First},{circuit.Schedule_Cable_To_Final},{circuit.VoltageDropPercentage:F3},{circuit.TotalEFLI:F3},{circuit.CheckResult},{circuit.ErrorMessage}");
                    }
                }
            }

            return content.ToString();
        }

        #endregion

        #region CSV Export

        /// <summary>
        /// Exports PowerBIM data to CSV format
        /// </summary>
        public async Task<bool> ExportToCSVAsync(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            string filePath,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Starting CSV export...");

                // Create CSV content
                var csvContent = await Task.Run(() => GenerateCSVContent(projectInfo, distributionBoards, progress));

                // Write to file
                //await File.WriteAllTextAsync(filePath, csvContent); // for .NET Core /.NET 5+
                await Task.Run(() => File.WriteAllText(filePath, csvContent));

                progress?.Report("CSV export completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                progress?.Report($"CSV export failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Generates CSV content for circuit data
        /// </summary>
        private string GenerateCSVContent(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress)
        {
            var content = new StringBuilder();

            // CSV Header
            content.AppendLine("DB Name,Circuit Number,Description,Poles,Elements,Current (A),Trip Rating (A),Cable To First,Cable To Final,Protective Device,Voltage Drop (%),EFLI (Ω),Diversified Current (A),Clearing Time (s),Result,Error Message");

            // Circuit Data
            foreach (var db in distributionBoards.Where(db => db.IsSelected))
            {
                progress?.Report($"Exporting circuits for DB: {db.Schedule_DB_Name}");

                if (db.Circuits != null)
                {
                    foreach (var circuit in db.Circuits)
                    {
                        content.AppendLine($"\"{db.Schedule_DB_Name}\",\"{circuit.CCT_Number}\",\"{circuit.Schedule_Description}\",{circuit.Number_Of_Poles},{circuit.Number_Of_Elements},{circuit.EffectiveCurrent:F2},{circuit.Schedule_Trip_Rating},\"{circuit.Schedule_Cable_To_First}\",\"{circuit.Schedule_Cable_To_Final}\",\"{circuit.Schedule_Protective_Device}\",{circuit.VoltageDropPercentage:F4},{circuit.TotalEFLI:F4},{circuit.DiversifiedCurrent:F2},{circuit.ClearingTime:F1},\"{circuit.CheckResult}\",\"{circuit.ErrorMessage}\"");
                    }
                }
            }

            return content.ToString();
        }

        #endregion

        #region Verification Report Export

        /// <summary>
        /// Exports a detailed verification report
        /// </summary>
        public async Task<bool> ExportVerificationReportAsync(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            string filePath,
            IProgress<string> progress = null)
        {
            try
            {
                progress?.Report("Generating verification report...");

                var reportContent = await Task.Run(() => GenerateVerificationReport(projectInfo, distributionBoards, progress));

                //await File.WriteAllTextAsync(filePath, reportContent); // for .NET Core /.NET 5+
                await Task.Run(() => File.WriteAllText(filePath, reportContent));

                progress?.Report("Verification report completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                progress?.Report($"Verification report failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Generates a comprehensive verification report
        /// </summary>
        private string GenerateVerificationReport(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            IProgress<string> progress)
        {
            var report = new StringBuilder();

            // Report Header
            report.AppendLine("POWERBIM VERIFICATION REPORT");
            report.AppendLine("=" + new string('=', 50));
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // Project Information
            report.AppendLine("PROJECT INFORMATION");
            report.AppendLine("-" + new string('-', 30));
            report.AppendLine($"Job Name: {projectInfo.JobName}");
            report.AppendLine($"Job Number: {projectInfo.JobNumber}");
            report.AppendLine($"Engineer: {projectInfo.Engineer}");
            report.AppendLine($"Verifier: {projectInfo.Verifier}");
            report.AppendLine($"Revision: {projectInfo.Revision}");
            report.AppendLine($"Database: {projectInfo.DatabasePath}");
            report.AppendLine();

            // Calculation Settings
            report.AppendLine("CALCULATION SETTINGS");
            report.AppendLine("-" + new string('-', 30));
            report.AppendLine($"System Voltage Drop: {(projectInfo.IsSystemVD5Percent ? "5%" : "7%")}");
            report.AppendLine($"Ambient Temperature: {projectInfo.AmbientTemp}°C");
            report.AppendLine($"Discrimination Multiplier: {projectInfo.DiscriminationTestMultiplier}");
            report.AppendLine($"Lighting Diversity: {projectInfo.DiversityLighting:P1}");
            report.AppendLine($"Power Diversity: {projectInfo.DiversityPower:P1}");
            report.AppendLine($"Other Diversity: {projectInfo.DiversityOther:P1}");
            report.AppendLine();

            // Summary Statistics
            var totalCircuits = distributionBoards.Sum(db => db.TotalCircuitCount);
            var totalPass = distributionBoards.Sum(db => db.Result_PassCount);
            var totalWarning = distributionBoards.Sum(db => db.Result_WarningCount);
            var totalFail = distributionBoards.Sum(db => db.Result_FailCount);

            report.AppendLine("SUMMARY STATISTICS");
            report.AppendLine("-" + new string('-', 30));
            report.AppendLine($"Total Distribution Boards: {distributionBoards.Count}");
            report.AppendLine($"Selected Distribution Boards: {distributionBoards.Count(db => db.IsSelected)}");
            report.AppendLine($"Total Circuits: {totalCircuits}");
            report.AppendLine($"Circuits Passed: {totalPass} ({(totalCircuits > 0 ? (double)totalPass / totalCircuits * 100 : 0):F1}%)");
            report.AppendLine($"Circuits with Warnings: {totalWarning} ({(totalCircuits > 0 ? (double)totalWarning / totalCircuits * 100 : 0):F1}%)");
            report.AppendLine($"Circuits Failed: {totalFail} ({(totalCircuits > 0 ? (double)totalFail / totalCircuits * 100 : 0):F1}%)");
            report.AppendLine();

            // Distribution Board Details
            report.AppendLine("DISTRIBUTION BOARD DETAILS");
            report.AppendLine("-" + new string('-', 50));

            foreach (var db in distributionBoards.Where(db => db.IsSelected))
            {
                progress?.Report($"Generating report for DB: {db.Schedule_DB_Name}");

                report.AppendLine($"DB: {db.Schedule_DB_Name}");
                report.AppendLine($"  Upstream Device Rating: {db.UpstreamDeviceRating}A");
                report.AppendLine($"  Device kA Rating: {db.DeviceKARating}kA");
                report.AppendLine($"  EFLI (R): {db.EFLI_R:F4}Ω");
                report.AppendLine($"  EFLI (X): {db.EFLI_X:F4}Ω");
                report.AppendLine($"  DB Voltage Drop: {db.DBVD:F2}%");
                report.AppendLine($"  PSCC: {db.PSCC:F2}kA");
                report.AppendLine($"  Total Circuits: {db.TotalCircuitCount}");
                report.AppendLine($"  Pass: {db.Result_PassCount}, Warning: {db.Result_WarningCount}, Fail: {db.Result_FailCount}");
                report.AppendLine($"  Overall Status: {(db.AllCircuitsPass ? "PASS" : "FAIL")}");

                // Failed circuits details
                if (db.Circuits != null)
                {
                    var failedCircuits = db.Circuits.Where(c => c.CheckResult == "Fail").ToList();
                    if (failedCircuits.Any())
                    {
                        report.AppendLine("  Failed Circuits:");
                        foreach (var circuit in failedCircuits)
                        {
                            report.AppendLine($"    {circuit.CCT_Number}: {circuit.ErrorMessage}");
                        }
                    }
                }

                report.AppendLine();
            }

            return report.ToString();
        }

        #endregion

        #region Export Dialog Helpers

        /// <summary>
        /// Shows export dialog and handles the export process
        /// </summary>
        public async Task<bool> ShowExportDialogAsync(
            PowerBIMProjectInfoModel projectInfo,
            ObservableCollection<PowerBIMDBDataModelEnhanced> distributionBoards,
            Window owner = null)
        {
            try
            {
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "Export PowerBIM Data",
                    Filter = "Excel Files (*.csv)|*.csv|Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                    DefaultExt = "csv",
                    FileName = $"PowerBIM_Export_{DateTime.Now:yyyy-MM-dd_HHmm}"
                };

                if (dialog.ShowDialog(owner) == true)
                {
                    var progress = new Progress<string>(message =>
                    {
                        // Could show progress dialog here
                        System.Diagnostics.Debug.WriteLine($"Export: {message}");
                    });

                    // Determine export type based on extension
                    var extension = Path.GetExtension(dialog.FileName).ToLower();

                    bool success = extension switch
                    {
                        ".csv" => await ExportToCSVAsync(projectInfo, distributionBoards, dialog.FileName, progress),
                        ".txt" => await ExportVerificationReportAsync(projectInfo, distributionBoards, dialog.FileName, progress),
                        _ => await ExportToExcelAsync(projectInfo, distributionBoards, dialog.FileName, progress)
                    };

                    if (success)
                    {
                        MessageBox.Show($"Export completed successfully!\n\nFile saved to:\n{dialog.FileName}",
                                      "Export Complete",
                                      MessageBoxButton.OK,
                                      MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Export failed. Please check the file path and try again.",
                                      "Export Failed",
                                      MessageBoxButton.OK,
                                      MessageBoxImage.Error);
                    }

                    return success;
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Export error: {ex.Message}",
                              "Export Error",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
                return false;
            }
        }

        #endregion
    }
}
