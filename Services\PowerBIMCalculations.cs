﻿using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Core electrical calculation algorithms for PowerBIM
    /// Ported from the original PowerBIM_Calculations class
    /// </summary>
    public static class PowerBIMCalculations
    {
        #region Voltage Drop Calculations

        /// <summary>
        /// Calculates final circuit voltage drop
        /// </summary>
        public static double CalculateFinalCircuitVoltDrop(PowerBIMCircuitDataModel circuit, PowerBIMCableDataModel cableToFirst, PowerBIMCableDataModel cableToFinal, bool useLinearDepreciating = true)
        {
            if (circuit == null || cableToFirst == null || cableToFinal == null)
                return 0;

            double lengthToFirst = circuit.LengthToFirst;
            double lengthTotal = circuit.LengthTotal;
            double current = circuit.EffectiveCurrent;

            if (useLinearDepreciating)
            {
                // Linear depreciating calculation (DEFAULT)
                double vdToFirst = cableToFirst.ZOperatingActive * lengthToFirst * 0.000001 * 2 * current;
                double vdToFinal = (cableToFinal.ZOperatingActive * (lengthTotal - lengthToFirst) * 0.000001 * 2 * current) / 2;
                return vdToFirst + vdToFinal;
            }
            else
            {
                // Full load calculation
                return (cableToFirst.ZOperatingActive * lengthToFirst * 0.000001 * 2 * current) +
                       (cableToFinal.ZOperatingActive * (lengthTotal - lengthToFirst) * 0.000001 * 2 * current);
            }
        }

        /// <summary>
        /// Converts voltage drop to percentage based on number of poles
        /// </summary>
        public static double CalculateFinalCircuitVoltDropPercentage(double voltageDropVolts, int numberOfPoles)
        {
            if (numberOfPoles == 3)
                return voltageDropVolts * 0.866 / 400; // 3-phase calculation
            else
                return voltageDropVolts / 230; // Single phase calculation
        }

        #endregion

        #region EFLI Calculations

        /// <summary>
        /// Performs Earth Fault Loop Impedance check
        /// </summary>
        public static bool CheckEFLI(PowerBIMCircuitDataModel circuit, PowerBIMBreakerDataModel breaker, double dbEFLI_R, double dbEFLI_X)
        {
            if (circuit == null || breaker == null)
                return false;

            // Pass if RCCB or RCBO
            if (breaker.BreakerName == "RCCB" || breaker.BreakerName == "RCBO")
            {
                circuit.TotalEFLI = 0;
                return true;
            }

            // Calculate total EFLI
            double cableEFLI_R = CalculateCableEFLI_R(circuit);
            double cableEFLI_X = CalculateCableEFLI_X(circuit);

            circuit.TotalEFLI = Math.Sqrt(Math.Pow(dbEFLI_R + cableEFLI_R, 2) + Math.Pow(dbEFLI_X + cableEFLI_X, 2));

            // Get maximum allowable EFLI from breaker
            double maxEFLI = breaker.ClearingTime <= 0.4 ? breaker.EFLI_04 : breaker.EFLI_50;

            return circuit.TotalEFLI <= maxEFLI;
        }

        /// <summary>
        /// Calculates cable resistance component of EFLI
        /// </summary>
        private static double CalculateCableEFLI_R(PowerBIMCircuitDataModel circuit)
        {
            // Simplified calculation - would need cable database lookup in full implementation
            return circuit.LengthTotal * 0.001; // Placeholder
        }

        /// <summary>
        /// Calculates cable reactance component of EFLI
        /// </summary>
        private static double CalculateCableEFLI_X(PowerBIMCircuitDataModel circuit)
        {
            // Simplified calculation - would need cable database lookup in full implementation
            return circuit.LengthTotal * 0.0005; // Placeholder
        }

        #endregion

        #region Breaker Discrimination

        /// <summary>
        /// Checks if downstream breaker can discriminate with upstream breaker
        /// </summary>
        public static bool CheckBreakerDiscrimination(PowerBIMBreakerDataModel downstreamBreaker, PowerBIMBreakerDataModel upstreamBreaker, double discriminationMultiplier = 1.5)
        {
            if (downstreamBreaker == null || upstreamBreaker == null)
                return false;

            if (!downstreamBreaker.IsValid || !upstreamBreaker.IsValid)
                return false;

            // Basic discrimination check - upstream should have higher rating
            return upstreamBreaker.TripRating >= (downstreamBreaker.TripRating * discriminationMultiplier);
        }

        #endregion

        #region Circuit Checks

        /// <summary>
        /// Main circuit check engine - performs all validation checks
        /// </summary>
        public static bool PowerBIMCircuitCheckEngine(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            if (circuit == null || db == null || projectInfo == null)
                return false;

            int passCount = 0;
            int totalChecks = 9;

            // Check 1: Data validation
            passCount += CircuitCheck1_Data(circuit) ? 1 : 0;

            // Check 2: Cable to first validation
            passCount += CircuitCheck2_CableToFirst(circuit) ? 1 : 0;

            // Check 3: Cable to final validation
            passCount += CircuitCheck3_CableToFinal(circuit) ? 1 : 0;

            // Check 4: Breaker discrimination
            passCount += CircuitCheck4_Discrimination(circuit, db, projectInfo) ? 1 : 0;

            // Check 5: Overload current
            passCount += CircuitCheck5_OverloadCurrent(circuit) ? 1 : 0;

            // Check 6: Rated current cable to first
            passCount += CircuitCheck6_RatedCurrentCableToFirst(circuit) ? 1 : 0;

            // Check 7: Rated current cable to final
            passCount += CircuitCheck7_RatedCurrentCableToFinal(circuit) ? 1 : 0;

            // Check 8: EFLI check
            passCount += CircuitCheck8_CheckEFLI(circuit, db) ? 1 : 0;

            // Check 9: Voltage drop check
            passCount += CircuitCheck9_VoltageDropCheck(circuit, projectInfo) ? 1 : 0;

            // Circuit passes if all checks pass
            return passCount == totalChecks;
        }

        #region Individual Circuit Checks

        private static bool CircuitCheck1_Data(PowerBIMCircuitDataModel circuit)
        {
            return circuit != null &&
                   !string.IsNullOrEmpty(circuit.CCT_Number) &&
                   circuit.Number_Of_Poles > 0 &&
                   circuit.Number_Of_Elements > 0;
        }

        private static bool CircuitCheck2_CableToFirst(PowerBIMCircuitDataModel circuit)
        {
            return !string.IsNullOrEmpty(circuit.Schedule_Cable_To_First) &&
                   circuit.Schedule_Cable_To_First != "Cannot Size Cable";
        }

        private static bool CircuitCheck3_CableToFinal(PowerBIMCircuitDataModel circuit)
        {
            return !string.IsNullOrEmpty(circuit.Schedule_Cable_To_Final) &&
                   circuit.Schedule_Cable_To_Final != "Cannot Size Cable";
        }

        private static bool CircuitCheck4_Discrimination(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db, PowerBIMProjectInfoModel projectInfo)
        {
            // Simplified discrimination check
            return circuit.Schedule_Trip_Rating <= db.UpstreamDeviceRating * projectInfo.DiscriminationTestMultiplier;
        }

        private static bool CircuitCheck5_OverloadCurrent(PowerBIMCircuitDataModel circuit)
        {
            return circuit.EffectiveCurrent <= circuit.Schedule_Trip_Rating;
        }

        private static bool CircuitCheck6_RatedCurrentCableToFirst(PowerBIMCircuitDataModel circuit)
        {
            // Would need cable database lookup for actual current rating
            return true; // Placeholder
        }

        private static bool CircuitCheck7_RatedCurrentCableToFinal(PowerBIMCircuitDataModel circuit)
        {
            // Would need cable database lookup for actual current rating
            return true; // Placeholder
        }

        private static bool CircuitCheck8_CheckEFLI(PowerBIMCircuitDataModel circuit, PowerBIMDBDataModelEnhanced db)
        {
            // Simplified EFLI check
            return circuit.TotalEFLI <= 1.0; // Placeholder threshold
        }

        private static bool CircuitCheck9_VoltageDropCheck(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            double maxVD = circuit.CCT_Is_Lighting ?
                (projectInfo.IsSystemVD5Percent ? 0.05 : 0.07) :
                (projectInfo.IsSystemVD5Percent ? 0.05 : 0.07);

            return circuit.VoltageDropPercentage <= maxVD;
        }

        #endregion

        #endregion

        #region Diversified Load Calculations

        /// <summary>
        /// Calculates diversified circuit load for DB totals
        /// </summary>
        public static double CalculateDiversifiedCircuitLoad(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            if (circuit == null || projectInfo == null)
                return 0;

            double baseCurrent = circuit.EffectiveCurrent;

            if (circuit.CCT_Is_Lighting)
            {
                return baseCurrent * projectInfo.DiversityLighting;
            }
            else if (circuit.CCT_Is_Power)
            {
                return baseCurrent * projectInfo.DiversityPower;
            }
            else
            {
                return baseCurrent * projectInfo.DiversityOther;
            }
        }

        #endregion

        #region Clearing Time Calculations

        /// <summary>
        /// Determines clearing time for circuit (0.4sec or 5sec)
        /// </summary>
        public static double DetermineClearingTime(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            if (circuit == null || projectInfo == null)
                return 0.4; // Default

            if (circuit.CCT_Is_Lighting)
            {
                return projectInfo.ClearingTimeLighting;
            }
            else if (circuit.CCT_Is_Power)
            {
                return projectInfo.ClearingTimePower;
            }
            else
            {
                return 0.4; // Default for other circuits
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Determines overall circuit result based on individual check results
        /// </summary>
        public static string DetermineCircuitResult(string cableResult, string breakerResult, string voltageDropResult)
        {
            var results = new[] { cableResult, breakerResult, voltageDropResult };

            if (results.Any(r => r == "Fail"))
                return "Fail";

            if (results.Any(r => r == "Warning"))
                return "Warning";

            return "Pass";
        }

        /// <summary>
        /// Generates error messages for failed circuits
        /// </summary>
        public static string GenerateErrorMessage(PowerBIMCircuitDataModel circuit, PowerBIMProjectInfoModel projectInfo)
        {
            var errors = new List<string>();

            if (circuit.VoltageDropPercentage > (projectInfo.IsSystemVD5Percent ? 0.05 : 0.07))
                errors.Add("Voltage drop exceeds maximum allowable");

            if (circuit.EffectiveCurrent > circuit.Schedule_Trip_Rating)
                errors.Add("Circuit current exceeds breaker rating");

            if (circuit.TotalEFLI > 1.0) // Placeholder threshold
                errors.Add("EFLI exceeds maximum allowable");

            return string.Join("; ", errors);
        }

        #endregion
    }
}
