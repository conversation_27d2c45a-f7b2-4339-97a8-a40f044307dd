﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing breaker/protective device data for WPF data binding
    /// </summary>
    public class PowerBIMBreakerDataModel : ObservableObject
    {
        #region Private Fields

        private string _breakerName = string.Empty;
        private int _breakerIndex;
        private string _curveType = string.Empty;
        private double _tripRating;
        private double _kaRating;
        private double _clearingTime;
        private double _i2t;
        private bool _isRCD;
        private double _rcdRating;
        private string _rcdType = string.Empty;
        private bool _dataGood = true;
        private string _errorMessage = string.Empty;
        private bool _warningUserDefBreakerSelected;
        private bool _warningDiscriminationFail;
        private bool _warningClearingTimeFail;
        private double _efli04;
        private double _efli50;

        #endregion

        #region Constructor

        public PowerBIMBreakerDataModel()
        {
            DataGood = true;
        }

        #endregion

        #region Basic Properties

        /// <summary>
        /// Gets or sets the breaker name
        /// </summary>
        [Required]
        public string BreakerName
        {
            get => _breakerName;
            set => SetProperty(ref _breakerName, value);
        }

        /// <summary>
        /// Gets or sets the breaker index
        /// </summary>
        public int BreakerIndex
        {
            get => _breakerIndex;
            set => SetProperty(ref _breakerIndex, value);
        }

        /// <summary>
        /// Gets or sets the curve type
        /// </summary>
        public string CurveType
        {
            get => _curveType;
            set => SetProperty(ref _curveType, value);
        }

        /// <summary>
        /// Gets or sets the trip rating
        /// </summary>
        [Range(0, double.MaxValue)]
        public double TripRating
        {
            get => _tripRating;
            set => SetProperty(ref _tripRating, value);
        }

        /// <summary>
        /// Gets or sets the kA rating
        /// </summary>
        [Range(0, double.MaxValue)]
        public double KARating
        {
            get => _kaRating;
            set => SetProperty(ref _kaRating, value);
        }

        /// <summary>
        /// Gets or sets the clearing time
        /// </summary>
        [Range(0, double.MaxValue)]
        public double ClearingTime
        {
            get => _clearingTime;
            set => SetProperty(ref _clearingTime, value);
        }

        /// <summary>
        /// Gets or sets the I²t value
        /// </summary>
        [Range(0, double.MaxValue)]
        public double I2t
        {
            get => _i2t;
            set => SetProperty(ref _i2t, value);
        }

        #endregion

        #region RCD Properties

        /// <summary>
        /// Gets or sets whether this is an RCD
        /// </summary>
        public bool IsRCD
        {
            get => _isRCD;
            set => SetProperty(ref _isRCD, value);
        }

        /// <summary>
        /// Gets or sets the RCD rating
        /// </summary>
        [Range(0, double.MaxValue)]
        public double RCDRating
        {
            get => _rcdRating;
            set => SetProperty(ref _rcdRating, value);
        }

        /// <summary>
        /// Gets or sets the RCD type
        /// </summary>
        public string RCDType
        {
            get => _rcdType;
            set => SetProperty(ref _rcdType, value);
        }

        #endregion

        #region Status Properties

        /// <summary>
        /// Gets or sets whether the data is good
        /// </summary>
        public bool DataGood
        {
            get => _dataGood;
            set => SetProperty(ref _dataGood, value);
        }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets whether user-defined breaker is selected warning
        /// </summary>
        public bool WarningUserDefBreakerSelected
        {
            get => _warningUserDefBreakerSelected;
            set => SetProperty(ref _warningUserDefBreakerSelected, value);
        }

        /// <summary>
        /// Gets or sets whether discrimination fails warning
        /// </summary>
        public bool WarningDiscriminationFail
        {
            get => _warningDiscriminationFail;
            set => SetProperty(ref _warningDiscriminationFail, value);
        }

        /// <summary>
        /// Gets or sets whether clearing time fails warning
        /// </summary>
        public bool WarningClearingTimeFail
        {
            get => _warningClearingTimeFail;
            set => SetProperty(ref _warningClearingTimeFail, value);
        }

        /// <summary>
        /// Gets or sets the EFLI value for 0.4 second clearing time
        /// </summary>
        public double EFLI_04
        {
            get => _efli04;
            set => SetProperty(ref _efli04, value);
        }

        /// <summary>
        /// Gets or sets the EFLI value for 5 second clearing time
        /// </summary>
        public double EFLI_50
        {
            get => _efli50;
            set => SetProperty(ref _efli50, value);
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets whether the breaker is valid
        /// </summary>
        public bool IsValid => DataGood && !string.IsNullOrEmpty(BreakerName) && BreakerName != "Cannot Size Breaker";

        /// <summary>
        /// Gets the breaker description for display
        /// </summary>
        public string BreakerDescription
        {
            get
            {
                if (!IsValid) return "Invalid Breaker";
                var description = BreakerName;
                if (TripRating > 0)
                    description += $" ({TripRating}A)";
                if (!string.IsNullOrEmpty(CurveType))
                    description += $" {CurveType}";
                if (IsRCD && RCDRating > 0)
                    description += $" RCD:{RCDRating}mA";
                return description;
            }
        }

        /// <summary>
        /// Gets the status color for UI display
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (!DataGood) return "Red";
                if (WarningUserDefBreakerSelected || WarningDiscriminationFail || WarningClearingTimeFail) return "Orange";
                if (IsValid) return "Green";
                return "Gray";
            }
        }

        /// <summary>
        /// Gets whether the breaker has warnings
        /// </summary>
        public bool HasWarnings => WarningUserDefBreakerSelected || WarningDiscriminationFail || WarningClearingTimeFail;

        /// <summary>
        /// Gets the warning message
        /// </summary>
        public string WarningMessage
        {
            get
            {
                var warnings = new List<string>();
                if (WarningUserDefBreakerSelected) warnings.Add("User-defined breaker");
                if (WarningDiscriminationFail) warnings.Add("Discrimination failure");
                if (WarningClearingTimeFail) warnings.Add("Clearing time failure");
                return string.Join(", ", warnings);
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Creates a null/invalid breaker entry
        /// </summary>
        public void CreateNullEntry()
        {
            BreakerName = "Cannot Size Breaker";
            BreakerIndex = 0;
            CurveType = string.Empty;
            TripRating = 0;
            KARating = 0;
            ClearingTime = 0;
            I2t = 0;
            IsRCD = false;
            RCDRating = 0;
            RCDType = string.Empty;
            DataGood = false;
            ErrorMessage = "Invalid Breaker Selected";
            WarningUserDefBreakerSelected = false;
            WarningDiscriminationFail = false;
            WarningClearingTimeFail = false;
        }

        /// <summary>
        /// Checks if this breaker can discriminate with another breaker
        /// </summary>
        public bool CanDiscriminateWith(PowerBIMBreakerDataModel upstreamBreaker)
        {
            if (upstreamBreaker == null || !IsValid || !upstreamBreaker.IsValid)
                return false;

            // Basic discrimination check - upstream should have higher rating
            return upstreamBreaker.TripRating > TripRating;
        }

        #endregion
    }
}
